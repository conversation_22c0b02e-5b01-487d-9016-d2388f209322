package com.geeksec.nta.alarm.controller;

import com.alibaba.fastjson2.JSON;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.dto.condition.AlarmCommonCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmListCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmRoleJudgeCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmStatusUpCondition;
import com.geeksec.nta.alarm.service.AlarmRelationAnalysisService;
import com.geeksec.nta.alarm.service.AlarmService;
import com.geeksec.nta.alarm.vo.AlarmTargetAggVo;
import com.geeksec.nta.alarm.vo.AlarmTypeAggVo;
import com.geeksec.nta.alarm.vo.KnowledgeAlarmVo;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.CharsetUtil;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 告警管理控制器
 * 负责告警的主要功能：CRUD、统计分析、关系分析、导出等
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@RestController
@RequestMapping("/alarm")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "告警管理", description = "告警管理相关接口")
public class AlarmController {
    
    private final AlarmService alarmService;
    private final AlarmRelationAnalysisService alarmRelationAnalysisService;
    
    // ==================== 告警数据管理 ====================
    
    /**
     * 获取告警列表（分页）
     */
    @PostMapping("/list")
    @Operation(summary = "获取告警列表", description = "根据条件分页查询告警列表")
    public ApiResponse<PageResultVo<Map<String, Object>>> getAlarmList(@RequestBody AlarmListCondition condition) {
        log.info("获取告警列表请求: {}", condition);
        return ApiResponse.success(alarmService.getAlarmList(condition));
    }
    
    /**
     * 获取告警详情
     */
    @GetMapping("/detail")
    @Operation(summary = "获取告警详情", description = "根据ES索引和告警ID获取告警详细信息")
    public ApiResponse<Map<String, Object>> getAlarmDetail(
            @RequestParam("esIndex") String esIndex,
            @RequestParam("alarmId") String alarmId) {
        log.info("获取告警详情请求: esIndex={}, alarmId={}", esIndex, alarmId);
        return ApiResponse.success(alarmService.getAlarmDetail(esIndex, alarmId));
    }
    
    /**
     * 更新告警状态
     */
    @PutMapping("/status")
    @Operation(summary = "更新告警状态", description = "批量更新告警的处理状态")
    public ApiResponse<String> updateAlarmStatus(@RequestBody AlarmStatusUpCondition condition) {
        log.info("更新告警状态请求: {}", condition);
        return ApiResponse.success(alarmService.updateAlarmStatus(condition));
    }
    
    /**
     * 删除告警文档
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除告警", description = "根据告警类型和ID列表批量删除告警")
    public ApiResponse<Long> deleteAlarms(@RequestBody Map<Integer, List<String>> condition) {
        log.info("批量删除告警请求: {}", condition);
        return ApiResponse.success(alarmService.deleteAlarms(condition));
    }
    
    /**
     * 删除所有告警
     */
    @DeleteMapping("/all")
    @Operation(summary = "删除所有告警", description = "清空所有告警数据，谨慎操作")
    public ApiResponse<String> deleteAllAlarms() {
        log.warn("删除所有告警数据请求");
        return ApiResponse.success(alarmService.deleteAllAlarms());
    }
    
    // ==================== 告警统计分析 ====================
    
    /**
     * 获取告警指标统计
     */
    @PostMapping("/target/agg")
    @Operation(summary = "获取告警指标统计", description = "获取各级别告警数量、攻击者数量、受害者数量等统计信息")
    public ApiResponse<AlarmTargetAggVo> getAlarmTargetAgg(@RequestBody AlarmCommonCondition condition) {
        log.info("获取告警指标统计请求: {}", condition);
        return ApiResponse.success(alarmService.getAlarmTargetAgg(condition));
    }
    
    /**
     * 获取攻击链路统计
     */
    @PostMapping("/type/agg")
    @Operation(summary = "获取攻击链路统计", description = "获取告警攻击链路的聚合统计信息")
    public ApiResponse<List<AlarmTypeAggVo.AttackChain>> getAttackChainAggr(@RequestBody AlarmCommonCondition condition) {
        log.info("获取攻击链路统计请求: {}", condition);
        return ApiResponse.success(alarmService.getAttackChainAggr(condition));
    }
    
    // ==================== 告警关系分析 ====================
    
    /**
     * 生成告警关系分析图谱
     */
    @PostMapping("/judge")
    @Operation(summary = "生成告警关系图谱", description = "根据告警信息分析攻击链路，构建节点和边的关系网络")
    public ApiResponse<Map<String, Object>> createAlarmRelationGraph(@RequestBody Map<String, Object> alarmMap) {
        log.info("生成告警关系图谱请求");
        return ApiResponse.success(alarmRelationAnalysisService.createAlarmRelationGraph(alarmMap));
    }
    
    /**
     * 基于IP角色生成告警关系扩展分析图谱
     */
    @PostMapping("/judge/role")
    @Operation(summary = "基于IP角色生成扩展图谱", description = "通过指定IP地址和角色信息，查询相关告警并进行关系扩展分析")
    public ApiResponse<Map<String, Object>> createAlarmRelationGraphByRole(@RequestBody AlarmRoleJudgeCondition condition) {
        log.info("基于IP角色生成扩展图谱请求: {}", condition);
        return ApiResponse.success(alarmRelationAnalysisService.createAlarmRelationGraphByRole(condition));
    }
    
    // ==================== 告警知识库 ====================
    
    /**
     * 获取告警知识库列表
     */
    @GetMapping("/knowledge")
    @Operation(summary = "获取知识库列表", description = "获取告警知识库全量列表")
    public ApiResponse<List<KnowledgeAlarmVo>> getKnowledgeAlarmList() {
        log.info("获取告警知识库列表请求");
        return ApiResponse.success(alarmService.getKnowledgeAlarmList());
    }
    
    /**
     * 初始化告警知识库
     */
    @PostMapping("/knowledge/init")
    @Operation(summary = "初始化知识库", description = "手动触发告警知识库和采集规则字典的初始化")
    public ApiResponse<String> initKnowledgeType() {
        log.info("手动初始化告警知识库请求");
        alarmService.initKnowledgeType();
        return ApiResponse.success("告警知识库初始化成功");
    }
    
    // ==================== 告警导出 ====================
    
    /**
     * 导出告警数据为CSV格式
     */
    @PostMapping("/export/csv")
    @Operation(summary = "导出CSV格式", description = "将告警数据导出为CSV格式文件")
    public ApiResponse<String> exportAlarmToCsv(@RequestBody AlarmListCondition condition, HttpServletResponse response) {
        log.info("导出CSV格式请求: {}", condition);
        return alarmService.exportAlarmToCsv(condition, response);
    }
    
    /**
     * 准备告警会话PCAP下载
     */
    @PostMapping("/download/prepare/pcap")
    @Operation(summary = "准备PCAP下载", description = "通过告警关联会话ID检索对应会话信息，生成PCAP下载列表")
    public ApiResponse prepareAlarmSessionPcap(
            @RequestParam("userId") Integer userId,
            @RequestParam("alarmSessionList") List<String> alarmSessionList,
            @RequestParam("alarmType") String alarmType,
            @RequestParam("alarmTime") Long alarmTime) {
        log.info("准备PCAP下载请求: userId={}, sessionCount={}, alarmType={}, alarmTime={}", 
                userId, alarmSessionList != null ? alarmSessionList.size() : 0, alarmType, alarmTime);
        return alarmService.prepareAlarmSessionPcap(userId, alarmSessionList, alarmType, alarmTime);
    }
}
