package com.geeksec.nta.alarm.interfaces.rest;

import com.geeksec.nta.alarm.application.service.AlarmSubscriptionQueryService;
import com.geeksec.nta.alarm.application.service.AlarmSubscriptionCommandService;
import com.geeksec.nta.alarm.interfaces.dto.common.ApiResult;
import com.geeksec.nta.alarm.interfaces.dto.common.PageResult;
import com.geeksec.nta.alarm.interfaces.dto.request.CreateSubscriptionRequest;
import com.geeksec.nta.alarm.interfaces.dto.request.UpdateSubscriptionRequest;
import com.geeksec.nta.alarm.interfaces.dto.request.SubscriptionQueryRequest;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmSubscriptionResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

/**
 * 告警订阅REST控制器
 * 负责告警订阅资源的RESTful API
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/subscriptions")
@RequiredArgsConstructor
@Tag(name = "告警订阅", description = "告警订阅管理相关接口")
public class AlarmSubscriptionRestController {
    
    private final AlarmSubscriptionQueryService subscriptionQueryService;
    private final AlarmSubscriptionCommandService subscriptionCommandService;
    
    // ==================== 订阅查询操作 ====================
    
    /**
     * 查询订阅列表
     */
    @GetMapping
    @Operation(summary = "查询订阅列表", description = "分页查询用户的告警订阅列表")
    public ApiResult<PageResult<AlarmSubscriptionResponse>> getSubscriptions(
            @Valid @ModelAttribute SubscriptionQueryRequest request,
            @AuthenticationPrincipal String currentUserId) {
        
        log.info("查询订阅列表: request={}, userId={}", request, currentUserId);
        
        var query = request.toQuery(currentUserId);
        var result = subscriptionQueryService.querySubscriptions(query);
        var pageResult = PageResult.of(result.getRecords(), result.getTotal(), 
                                     result.getCurrent(), result.getSize());
        
        return ApiResult.success(pageResult, "查询成功");
    }
    
    /**
     * 查询订阅详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询订阅详情", description = "根据订阅ID查询详细信息")
    public ApiResult<AlarmSubscriptionResponse> getSubscription(
            @Parameter(description = "订阅ID") @PathVariable String id,
            @AuthenticationPrincipal String currentUserId) {
        
        log.info("查询订阅详情: id={}, userId={}", id, currentUserId);
        
        var subscriptionId = new com.geeksec.nta.alarm.domain.valueobject.SubscriptionId(id);
        var userId = new com.geeksec.nta.alarm.domain.valueobject.UserId(currentUserId);
        
        var subscription = subscriptionQueryService.getSubscription(subscriptionId, userId);
        
        if (subscription.isPresent()) {
            return ApiResult.success(subscription.get(), "查询成功");
        } else {
            return ApiResult.notFound("订阅");
        }
    }
    
    // ==================== 订阅管理操作 ====================
    
    /**
     * 创建订阅
     */
    @PostMapping
    @Operation(summary = "创建告警订阅", description = "创建新的告警订阅规则")
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResult<String> createSubscription(
            @Valid @RequestBody CreateSubscriptionRequest request,
            @AuthenticationPrincipal String currentUserId) {
        
        log.info("创建订阅: request={}, userId={}", request, currentUserId);
        
        var command = request.toCommand(currentUserId);
        var subscriptionId = subscriptionCommandService.createSubscription(command);
        
        return ApiResult.success(subscriptionId.value(), "订阅创建成功");
    }
    
    /**
     * 更新订阅
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新告警订阅", description = "完整更新告警订阅信息")
    public ApiResult<Void> updateSubscription(
            @Parameter(description = "订阅ID") @PathVariable String id,
            @Valid @RequestBody UpdateSubscriptionRequest request,
            @AuthenticationPrincipal String currentUserId) {
        
        log.info("更新订阅: id={}, request={}, userId={}", id, request, currentUserId);
        
        var command = request.toCommand(id, currentUserId);
        boolean success = subscriptionCommandService.updateSubscription(command);
        
        if (success) {
            return ApiResult.success("订阅更新成功");
        } else {
            return ApiResult.businessError("订阅更新失败");
        }
    }
    
    /**
     * 删除订阅
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除告警订阅", description = "删除指定的告警订阅")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public ApiResult<Void> deleteSubscription(
            @Parameter(description = "订阅ID") @PathVariable String id,
            @AuthenticationPrincipal String currentUserId) {
        
        log.info("删除订阅: id={}, userId={}", id, currentUserId);
        
        var command = new com.geeksec.nta.alarm.application.command.DeleteSubscriptionCommand(
                new com.geeksec.nta.alarm.domain.valueobject.SubscriptionId(id),
                currentUserId,
                "用户删除"
        );
        
        boolean success = subscriptionCommandService.deleteSubscription(command);
        
        if (success) {
            return ApiResult.success("订阅删除成功");
        } else {
            return ApiResult.businessError("订阅删除失败");
        }
    }
    
    // ==================== 订阅状态管理 ====================
    
    /**
     * 启用/禁用订阅
     */
    @PatchMapping("/{id}/status")
    @Operation(summary = "启用/禁用订阅", description = "切换订阅的启用状态")
    public ApiResult<Void> toggleSubscription(
            @Parameter(description = "订阅ID") @PathVariable String id,
            @Parameter(description = "是否启用") @RequestParam Boolean enabled,
            @AuthenticationPrincipal String currentUserId) {
        
        log.info("切换订阅状态: id={}, enabled={}, userId={}", id, enabled, currentUserId);
        
        var command = new com.geeksec.nta.alarm.application.command.ToggleSubscriptionCommand(
                new com.geeksec.nta.alarm.domain.valueobject.SubscriptionId(id),
                enabled,
                currentUserId
        );
        
        boolean success = subscriptionCommandService.toggleSubscription(command);
        
        if (success) {
            String message = enabled ? "订阅已启用" : "订阅已禁用";
            return ApiResult.success(message);
        } else {
            return ApiResult.businessError("状态切换失败");
        }
    }
    
    /**
     * 复制订阅
     */
    @PostMapping("/{id}/copy")
    @Operation(summary = "复制订阅", description = "复制现有订阅创建新的订阅")
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResult<String> copySubscription(
            @Parameter(description = "源订阅ID") @PathVariable String id,
            @Parameter(description = "新订阅名称") @RequestParam String newName,
            @AuthenticationPrincipal String currentUserId) {
        
        log.info("复制订阅: sourceId={}, newName={}, userId={}", id, newName, currentUserId);
        
        var sourceSubscriptionId = new com.geeksec.nta.alarm.domain.valueobject.SubscriptionId(id);
        var newSubscriptionId = subscriptionCommandService.copySubscription(
                sourceSubscriptionId, newName, currentUserId);
        
        return ApiResult.success(newSubscriptionId.value(), "订阅复制成功");
    }
}
