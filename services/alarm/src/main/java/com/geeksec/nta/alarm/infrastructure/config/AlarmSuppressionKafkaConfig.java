package com.geeksec.nta.alarm.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 告警抑制规则Kafka配置
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "nta.alarm.suppression.kafka")
public class AlarmSuppressionKafkaConfig {
    
    /**
     * Kafka主题名称
     */
    private String topic = "alarm-suppression-changes";
    
    /**
     * Kafka Bootstrap服务器地址
     */
    private String bootstrapServers = "localhost:9092";
}
