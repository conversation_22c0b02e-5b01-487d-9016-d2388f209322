package com.geeksec.nta.alarm.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.nta.alarm.dto.subscription.NotificationSubscriptionDto;
import com.geeksec.nta.alarm.entity.AlarmSubscription;
import com.geeksec.nta.alarm.event.SubscriptionChangeEvent;
import com.geeksec.nta.alarm.service.SubscriptionEventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

/**
 * 订阅事件发布服务实现
 * 负责向 Kafka 发送订阅变更事件
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SubscriptionEventPublisherImpl implements SubscriptionEventPublisher {
    
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;
    
    @Value("${nta.alarm.subscription.kafka.topic:subscription-changes}")
    private String subscriptionChangesTopic;
    
    /**
     * 发布订阅创建事件
     */
    public void publishSubscriptionCreated(AlarmSubscription subscription, String operatedBy) {
        try {
            String subscriptionData = convertToSubscriptionData(subscription);
            SubscriptionChangeEvent event = SubscriptionChangeEvent.createCreatedEvent(
                    subscription.getId(),
                    subscription.getUserId(),
                    subscriptionData,
                    operatedBy
            );
            
            publishEvent(event);
            log.info("发布订阅创建事件成功，订阅ID: {}", subscription.getId());
            
        } catch (Exception e) {
            log.error("发布订阅创建事件失败，订阅ID: {}", subscription.getId(), e);
        }
    }
    
    /**
     * 发布订阅更新事件
     */
    public void publishSubscriptionUpdated(AlarmSubscription subscription, String operatedBy) {
        try {
            String subscriptionData = convertToSubscriptionData(subscription);
            SubscriptionChangeEvent event = SubscriptionChangeEvent.createUpdatedEvent(
                    subscription.getId(),
                    subscription.getUserId(),
                    subscriptionData,
                    operatedBy
            );
            
            publishEvent(event);
            log.info("发布订阅更新事件成功，订阅ID: {}", subscription.getId());
            
        } catch (Exception e) {
            log.error("发布订阅更新事件失败，订阅ID: {}", subscription.getId(), e);
        }
    }
    
    /**
     * 发布订阅删除事件
     */
    public void publishSubscriptionDeleted(String subscriptionId, String userId, String operatedBy) {
        try {
            SubscriptionChangeEvent event = SubscriptionChangeEvent.createDeletedEvent(
                    subscriptionId,
                    userId,
                    operatedBy
            );
            
            publishEvent(event);
            log.info("发布订阅删除事件成功，订阅ID: {}", subscriptionId);
            
        } catch (Exception e) {
            log.error("发布订阅删除事件失败，订阅ID: {}", subscriptionId, e);
        }
    }
    
    /**
     * 发布订阅状态变更事件
     */
    public void publishSubscriptionStatusChanged(AlarmSubscription subscription, boolean enabled, String operatedBy) {
        try {
            String subscriptionData = convertToSubscriptionData(subscription);
            SubscriptionChangeEvent event = SubscriptionChangeEvent.createStatusChangeEvent(
                    subscription.getId(),
                    subscription.getUserId(),
                    subscriptionData,
                    enabled,
                    operatedBy
            );
            
            publishEvent(event);
            log.info("发布订阅状态变更事件成功，订阅ID: {}, 状态: {}", subscription.getId(), enabled);
            
        } catch (Exception e) {
            log.error("发布订阅状态变更事件失败，订阅ID: {}", subscription.getId(), e);
        }
    }
    
    /**
     * 发布事件到 Kafka
     */
    private void publishEvent(SubscriptionChangeEvent event) {
        try {
            String eventJson = objectMapper.writeValueAsString(event);
            String key = event.getSubscriptionId();
            
            ListenableFuture<SendResult<String, String>> future = 
                    kafkaTemplate.send(subscriptionChangesTopic, key, eventJson);
            
            future.addCallback(new ListenableFutureCallback<SendResult<String, String>>() {
                @Override
                public void onSuccess(SendResult<String, String> result) {
                    log.debug("订阅事件发送成功，事件ID: {}, 主题: {}, 分区: {}, 偏移量: {}",
                            event.getEventId(),
                            result.getRecordMetadata().topic(),
                            result.getRecordMetadata().partition(),
                            result.getRecordMetadata().offset());
                }
                
                @Override
                public void onFailure(Throwable ex) {
                    log.error("订阅事件发送失败，事件ID: {}, 错误: {}", event.getEventId(), ex.getMessage(), ex);
                }
            });
            
        } catch (Exception e) {
            log.error("序列化订阅事件失败，事件ID: {}", event.getEventId(), e);
            throw new RuntimeException("发布订阅事件失败", e);
        }
    }
    
    /**
     * 转换为订阅数据
     */
    private String convertToSubscriptionData(AlarmSubscription subscription) throws Exception {
        NotificationSubscriptionDto dto = NotificationSubscriptionDto.builder()
                .subscriptionId(subscription.getId())
                .userId(subscription.getUserId())
                .username(subscription.getUserId()) // 这里可以根据需要查询用户名
                .subscriptionName(subscription.getSubscriptionName())
                .enabled(subscription.getEnabled())
                .priorityLevel(subscription.getPriorityLevel())
                .rules(subscription.getMatchRules())
                .channels(subscription.getNotificationChannels())
                .frequencyType(subscription.getFrequencyType())
                .frequency(subscription.getFrequencyConfig())
                .quietHours(subscription.getQuietHoursConfig())
                .triggerCount(subscription.getTriggerCount())
                .lastTriggeredTime(subscription.getLastTriggeredTime())
                .createTime(subscription.getCreatedTime())
                .updateTime(subscription.getUpdatedTime())
                .build();
        
        return objectMapper.writeValueAsString(dto);
    }
    
    /**
     * 获取订阅变更主题名称
     */
    public String getSubscriptionChangesTopic() {
        return subscriptionChangesTopic;
    }
}
