package com.geeksec.nta.alarm.service;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.dto.condition.AlarmCommonCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmListCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmStatusUpCondition;
import com.geeksec.nta.alarm.vo.AlarmTargetAggVo;
import com.geeksec.nta.alarm.vo.AlarmTypeAggVo;
import com.geeksec.nta.alarm.vo.KnowledgeAlarmVo;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;
import java.util.Map;

/**
 * 告警服务接口
 * 负责告警的主要业务功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmService {
    
    // ==================== 告警数据管理 ====================
    
    /**
     * 获取告警列表（分页）
     * 
     * @param condition 查询条件
     * @return 分页结果
     */
    PageResultVo<Map<String, Object>> getAlarmList(AlarmListCondition condition);
    
    /**
     * 获取告警详情
     * 
     * @param esIndex ES索引
     * @param alarmId 告警ID
     * @return 告警详情
     */
    Map<String, Object> getAlarmDetail(String esIndex, String alarmId);
    
    /**
     * 更新告警状态
     * 
     * @param condition 更新条件
     * @return 更新结果
     */
    String updateAlarmStatus(AlarmStatusUpCondition condition);
    
    /**
     * 删除告警文档
     * 
     * @param condition 删除条件 (告警类型 -> 告警ID列表)
     * @return 删除的数量
     */
    Long deleteAlarms(Map<Integer, List<String>> condition);
    
    /**
     * 删除所有告警信息
     * 
     * @return 删除结果
     */
    String deleteAllAlarms();
    
    // ==================== 告警统计分析 ====================
    
    /**
     * 获取告警指标统计信息
     * 包括各级别告警数量、攻击者数量、受害者数量等
     * 
     * @param condition 查询条件
     * @return 告警指标统计结果
     */
    AlarmTargetAggVo getAlarmTargetAgg(AlarmCommonCondition condition);
    
    /**
     * 获取告警攻击链路统计
     * 用于告警页面的攻击链路展示
     * 
     * @param condition 查询条件
     * @return 攻击链路统计结果
     */
    List<AlarmTypeAggVo.AttackChain> getAttackChainAggr(AlarmCommonCondition condition);
    
    // ==================== 告警知识库 ====================
    
    /**
     * 初始化告警知识库和采集规则字典
     * 系统启动时调用，加载告警规则配置
     */
    void initKnowledgeType();
    
    /**
     * 获取告警知识库全量列表
     * 
     * @return 知识库列表
     */
    List<KnowledgeAlarmVo> getKnowledgeAlarmList();
    
    // ==================== 告警导出 ====================
    
    /**
     * 导出告警数据为CSV格式
     * 
     * @param condition 查询条件
     * @param response HTTP响应
     * @return 导出结果
     */
    ApiResponse<String> exportAlarmToCsv(AlarmListCondition condition, HttpServletResponse response);
    
    /**
     * 准备告警会话PCAP下载
     * 通过告警关联会话ID检索对应会话信息，生成PCAP下载列表
     * 
     * @param userId 用户ID
     * @param alarmSessionList 告警会话ID列表
     * @param alarmType 告警类型
     * @param alarmTime 告警时间
     * @return 准备结果
     */
    ApiResponse prepareAlarmSessionPcap(Integer userId, List<String> alarmSessionList, String alarmType, Long alarmTime);
}
