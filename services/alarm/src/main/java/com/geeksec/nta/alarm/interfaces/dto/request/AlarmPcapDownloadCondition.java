package com.geeksec.nta.alarm.interfaces.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: jerryzhou
 * @date: 2024/5/29 11:27
 * @Description: 告警抓包下载条件（支持批量下载）
 **/
@Data
public class AlarmPcapDownloadCondition {

    /**
     * 待导出告警对应会话列表
     */
    @JsonProperty(value = "alarm_session_list")
    private List<String> alarmSessionList;

    /**
     * 告警类型(模型 防御 规则)
     */
    @JsonProperty(value = "alarm_type")
    private String alarType;

    /**
     * 告警时间
     */
    @JsonProperty(value = "alarm_time")
    private Long alarmTime;

    /**
     * 导出用户ID
     */
    @JsonProperty(value = "user_id")
    private Integer userId;
}
