package com.geeksec.nta.alarm.infrastructure.mapper;

import com.mybatisflex.core.BaseMapper;
import com.geeksec.nta.alarm.domain.aggregate.NotificationTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通知模板 Mapper
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Mapper
public interface NotificationTemplateMapper extends BaseMapper<NotificationTemplate> {
    
    /**
     * 根据模板类型查询模板列表
     * 
     * @param templateType 模板类型
     * @return 模板列表
     */
    List<NotificationTemplate> selectByTemplateType(@Param("templateType") NotificationTemplate.TemplateType templateType);
    
    /**
     * 查询默认模板
     * 
     * @param templateType 模板类型
     * @return 默认模板
     */
    NotificationTemplate selectDefaultByType(@Param("templateType") NotificationTemplate.TemplateType templateType);
    
    /**
     * 查询所有可用模板
     * 
     * @return 模板列表
     */
    List<NotificationTemplate> selectAllAvailable();
}
