package com.geeksec.nta.alarm.dto.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class AnalysisBaseCondition {

    /**
     * 当前查询用户ID
     */
    @JsonProperty("user_id")
    private Integer userId;

    /**
     * 创建查询模板时的备注
     */
    @JsonProperty("template_remark")
    private String templateRemark;

    /**
     * 是否升序
     */
    @JsonProperty("asc")
    private Boolean asc=true;

    /**
     * 当前页
     */
    @JsonProperty("current_page")
    @Schema(description = "当前页")
    private Integer currentPage = 1;

    /**
     * 当前页展示数量
     */
    @JsonProperty("page_size")
    @Schema(description = "当前页展示数量")
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    @JsonProperty("order_field")
    private String orderField;

    /**
     * 查询任务ID集合
     */
    @JsonProperty("task_id")
    private List<Integer> taskId;

    /**
     * 开始时间 & 结束时间
     */
    @JsonProperty("time_range")
    private TimeRange timeRange;

    /**
     * 查询条件
     */
    @JsonProperty("query")
    private List<QueryOb> query;

    /**
     * 查询条件（前端用于回写）
     */
    @JsonProperty("query_cn")
    private List<Object> queryCn;

    /**
     * 是否聚合查询(标签条件过滤)
     */
    @JsonProperty("aggr_query")
    private Boolean aggrQuery;

    /**
     * 是否为定时任务查询
     */
    private Boolean isTask = false;

    /**
     * 标签查询条件
     */
    @JsonProperty("tag_query")
    private Map<String,List<List<String>>> tagQuery;

    @Data
    public static class QueryOb{
        @JsonProperty("bool_search")
        private String boolSearch;
        /** 查询集合 */
        private List<SearchInfo> search;
    }

    @Data
    public static class SearchInfo{
        /** 查询key */
        private String target;
        /** 查询内容 */
        private List<String> val;

        /** 是否为ES字段查询 */
        @JsonProperty("es_field")
        private Boolean esField;

    }

    @Data
    public static class TimeRange{
        /** 开始时间 */
        private Long left = -1L;
        /** 结束时间 */
        private Long right = -1L;
    }
}
