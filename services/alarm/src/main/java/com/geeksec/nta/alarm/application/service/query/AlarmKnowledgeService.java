package com.geeksec.nta.alarm.application.service.query;

import com.geeksec.nta.alarm.interfaces.dto.response.AlarmKnowledgeResponse;

import java.util.List;
import java.util.Optional;

/**
 * 告警知识库应用服务
 * 负责处理告警知识库相关的业务逻辑
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmKnowledgeService {
    
    /**
     * 获取告警知识库列表
     * 
     * @return 知识库列表
     */
    List<AlarmKnowledgeResponse> getKnowledgeList();
    
    /**
     * 根据告警类型获取知识库信息
     * 
     * @param alarmType 告警类型
     * @return 知识库信息
     */
    Optional<AlarmKnowledgeResponse> getKnowledgeByType(String alarmType);
    
    /**
     * 根据知识库ID获取详细信息
     * 
     * @param knowledgeId 知识库ID
     * @return 知识库详情
     */
    Optional<AlarmKnowledgeResponse> getKnowledgeById(Integer knowledgeId);
    
    /**
     * 搜索知识库
     * 
     * @param keyword 关键词
     * @return 搜索结果
     */
    List<AlarmKnowledgeResponse> searchKnowledge(String keyword);
    
    /**
     * 初始化告警知识库
     * 从配置文件或数据库加载知识库数据
     * 
     * @return 初始化结果
     */
    InitializationResult initializeKnowledge();
    
    /**
     * 刷新告警知识库缓存
     * 
     * @return 刷新结果
     */
    RefreshResult refreshKnowledgeCache();
    
    /**
     * 获取告警类型映射
     * 
     * @return 类型映射 (代码 -> 名称)
     */
    java.util.Map<String, String> getAlarmTypeMapping();
    
    /**
     * 获取攻击链模型
     * 
     * @return 攻击链模型数据
     */
    List<AttackChainModel> getAttackChainModel();
    
    /**
     * 根据告警类型获取相关的攻击链阶段
     * 
     * @param alarmType 告警类型
     * @return 攻击链阶段
     */
    Optional<String> getAttackChainStage(String alarmType);
    
    /**
     * 初始化结果
     */
    record InitializationResult(
        boolean success,
        int loadedCount,
        String message,
        List<String> errors
    ) {}
    
    /**
     * 刷新结果
     */
    record RefreshResult(
        boolean success,
        int refreshedCount,
        String message
    ) {}
    
    /**
     * 攻击链模型
     */
    record AttackChainModel(
        String stage,
        String stageName,
        String description,
        List<String> alarmTypes,
        int order
    ) {}
}
