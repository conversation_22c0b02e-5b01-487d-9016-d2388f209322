package com.geeksec.nta.alarm.application.service.query;

import com.geeksec.common.entity.PageResultVoVo;
import com.geeksec.nta.alarm.application.query.AlarmDetailQuery;
import com.geeksec.nta.alarm.application.query.AlarmListQuery;
import com.geeksec.nta.alarm.domain.valueobject.AlarmId;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmDetailResponse;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmListResponse;

/**
 * 告警查询应用服务
 * 负责处理所有告警查询相关的业务逻辑
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmQueryService {
    
    /**
     * 分页查询告警列表
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    PageResultVoVo<AlarmListResponse> queryAlarmList(AlarmListQuery query);
    
    /**
     * 查询告警详情
     * 
     * @param query 查询条件
     * @return 告警详情
     */
    AlarmDetailResponse queryAlarmDetail(AlarmDetailQuery query);
    
    /**
     * 根据ID查询告警详情
     * 
     * @param alarmId 告警ID
     * @return 告警详情
     */
    AlarmDetailResponse queryAlarmById(AlarmId alarmId);
    
    /**
     * 检查告警是否存在
     * 
     * @param alarmId 告警ID
     * @return 是否存在
     */
    boolean existsById(AlarmId alarmId);
    
    /**
     * 统计告警总数
     * 
     * @param query 查询条件
     * @return 总数
     */
    long countAlarms(AlarmListQuery query);
}
