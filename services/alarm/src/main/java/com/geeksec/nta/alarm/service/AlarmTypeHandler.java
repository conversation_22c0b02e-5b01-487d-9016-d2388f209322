package com.geeksec.nta.alarm.service;

import com.geeksec.nta.alarm.domain.model.AlarmAnalysisContext;
import com.geeksec.nta.alarm.domain.model.GraphBuilder;

/**
 * 告警类型处理器接口
 * 使用策略模式处理不同类型的告警分析逻辑
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmTypeHandler {
    
    /**
     * 判断是否支持处理指定的告警类型
     * 
     * @param alarmKnowledgeId 告警知识库ID
     * @return 是否支持
     */
    boolean supports(Integer alarmKnowledgeId);
    
    /**
     * 处理告警分析逻辑
     * 
     * @param context 告警分析上下文
     * @param builder 图谱构建器
     */
    void analyze(AlarmAnalysisContext context, GraphBuilder builder);
    
    /**
     * 获取处理器名称
     * 
     * @return 处理器名称
     */
    default String getHandlerName() {
        return this.getClass().getSimpleName();
    }
}
