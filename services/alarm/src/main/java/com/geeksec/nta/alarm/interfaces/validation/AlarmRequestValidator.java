package com.geeksec.nta.alarm.interfaces.validation;

import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 告警请求验证器
 * 提供自定义验证注解和验证逻辑
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Component
public class AlarmRequestValidator {
    
    private static final Pattern IP_PATTERN = Pattern.compile(
            "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");
    
    private static final Pattern ALARM_ID_PATTERN = Pattern.compile("^[a-zA-Z0-9_-]{1,64}$");
    
    private static final List<String> VALID_ALARM_STATUSES = List.of(
            "PENDING", "PROCESSING", "RESOLVED", "IGNORED", "FALSE_POSITIVE");
    
    private static final List<String> VALID_SEVERITIES = List.of(
            "LOW", "MEDIUM", "HIGH", "CRITICAL");
    
    private static final List<String> VALID_SORT_FIELDS = List.of(
            "createTime", "updateTime", "severity", "alarmType", "status");
    
    /**
     * 验证IP地址格式
     */
    public static boolean isValidIpAddress(String ip) {
        return ip != null && IP_PATTERN.matcher(ip).matches();
    }
    
    /**
     * 验证告警ID格式
     */
    public static boolean isValidAlarmId(String alarmId) {
        return alarmId != null && ALARM_ID_PATTERN.matcher(alarmId).matches();
    }
    
    /**
     * 验证告警状态
     */
    public static boolean isValidAlarmStatus(String status) {
        return status != null && VALID_ALARM_STATUSES.contains(status.toUpperCase());
    }
    
    /**
     * 验证严重程度
     */
    public static boolean isValidSeverity(String severity) {
        return severity != null && VALID_SEVERITIES.contains(severity.toUpperCase());
    }
    
    /**
     * 验证排序字段
     */
    public static boolean isValidSortField(String sortField) {
        return sortField != null && VALID_SORT_FIELDS.contains(sortField);
    }
    
    /**
     * 验证时间范围
     */
    public static boolean isValidTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            return true; // 允许为空
        }
        return !startTime.isAfter(endTime);
    }
}

/**
 * IP地址验证注解
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValidIpAddress.Validator.class)
@Documented
@interface ValidIpAddress {
    
    String message() default "IP地址格式不正确";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    class Validator implements ConstraintValidator<ValidIpAddress, String> {
        
        @Override
        public boolean isValid(String value, ConstraintValidatorContext context) {
            if (value == null || value.trim().isEmpty()) {
                return true; // 允许为空，使用@NotNull等注解控制必填
            }
            return AlarmRequestValidator.isValidIpAddress(value);
        }
    }
}

/**
 * 告警ID验证注解
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValidAlarmId.Validator.class)
@Documented
@interface ValidAlarmId {
    
    String message() default "告警ID格式不正确";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    class Validator implements ConstraintValidator<ValidAlarmId, String> {
        
        @Override
        public boolean isValid(String value, ConstraintValidatorContext context) {
            if (value == null || value.trim().isEmpty()) {
                return true;
            }
            return AlarmRequestValidator.isValidAlarmId(value);
        }
    }
}

/**
 * 告警状态验证注解
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValidAlarmStatus.Validator.class)
@Documented
@interface ValidAlarmStatus {
    
    String message() default "告警状态不正确";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    class Validator implements ConstraintValidator<ValidAlarmStatus, String> {
        
        @Override
        public boolean isValid(String value, ConstraintValidatorContext context) {
            if (value == null || value.trim().isEmpty()) {
                return true;
            }
            return AlarmRequestValidator.isValidAlarmStatus(value);
        }
    }
}

/**
 * 严重程度验证注解
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValidSeverity.Validator.class)
@Documented
@interface ValidSeverity {
    
    String message() default "严重程度不正确";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    class Validator implements ConstraintValidator<ValidSeverity, String> {
        
        @Override
        public boolean isValid(String value, ConstraintValidatorContext context) {
            if (value == null || value.trim().isEmpty()) {
                return true;
            }
            return AlarmRequestValidator.isValidSeverity(value);
        }
    }
}

/**
 * 时间范围验证注解
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValidTimeRange.Validator.class)
@Documented
@interface ValidTimeRange {
    
    String message() default "时间范围不正确，开始时间不能晚于结束时间";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    String startTimeField() default "startTime";
    
    String endTimeField() default "endTime";
    
    class Validator implements ConstraintValidator<ValidTimeRange, Object> {
        
        private String startTimeField;
        private String endTimeField;
        
        @Override
        public void initialize(ValidTimeRange constraintAnnotation) {
            this.startTimeField = constraintAnnotation.startTimeField();
            this.endTimeField = constraintAnnotation.endTimeField();
        }
        
        @Override
        public boolean isValid(Object value, ConstraintValidatorContext context) {
            if (value == null) {
                return true;
            }
            
            try {
                var startTime = getFieldValue(value, startTimeField, LocalDateTime.class);
                var endTime = getFieldValue(value, endTimeField, LocalDateTime.class);
                
                return AlarmRequestValidator.isValidTimeRange(startTime, endTime);
                
            } catch (Exception e) {
                return false;
            }
        }
        
        @SuppressWarnings("unchecked")
        private <T> T getFieldValue(Object object, String fieldName, Class<T> fieldType) throws Exception {
            var field = object.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return (T) field.get(object);
        }
    }
}
