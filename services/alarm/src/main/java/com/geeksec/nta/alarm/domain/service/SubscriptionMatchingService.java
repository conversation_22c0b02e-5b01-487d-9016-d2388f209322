package com.geeksec.nta.alarm.domain.service;

import com.geeksec.nta.alarm.domain.aggregate.Alarm;
import com.geeksec.nta.alarm.domain.aggregate.AlarmSubscription;
import com.geeksec.nta.alarm.domain.valueobject.SubscriptionRule;

import java.util.List;

/**
 * 订阅匹配领域服务
 * 负责告警与订阅规则的匹配逻辑
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface SubscriptionMatchingService {
    
    /**
     * 检查告警是否匹配订阅
     * 
     * @param alarm 告警
     * @param subscription 订阅
     * @return 匹配结果
     */
    MatchResult matches(Alarm alarm, AlarmSubscription subscription);
    
    /**
     * 查找匹配的订阅
     * 
     * @param alarm 告警
     * @param subscriptions 订阅列表
     * @return 匹配的订阅列表
     */
    List<MatchedSubscription> findMatchingSubscriptions(Alarm alarm, List<AlarmSubscription> subscriptions);
    
    /**
     * 测试订阅规则
     * 
     * @param alarm 测试告警
     * @param rules 订阅规则列表
     * @return 测试结果
     */
    TestResult testSubscriptionRules(Alarm alarm, List<SubscriptionRule> rules);
    
    /**
     * 验证订阅规则的有效性
     * 
     * @param rules 订阅规则列表
     * @return 验证结果
     */
    ValidationResult validateSubscriptionRules(List<SubscriptionRule> rules);
    
    /**
     * 匹配结果
     */
    record MatchResult(
        boolean matched,
        int matchedRulesCount,
        int totalRulesCount,
        List<RuleMatchDetail> ruleDetails,
        String message
    ) {}
    
    /**
     * 匹配的订阅
     */
    record MatchedSubscription(
        AlarmSubscription subscription,
        MatchResult matchResult,
        int priority
    ) {}
    
    /**
     * 测试结果
     */
    record TestResult(
        boolean passed,
        int passedRulesCount,
        int totalRulesCount,
        List<RuleTestDetail> ruleDetails,
        String summary
    ) {}
    
    /**
     * 验证结果
     */
    record ValidationResult(
        boolean valid,
        List<String> errors,
        List<String> warnings,
        String message
    ) {}
    
    /**
     * 规则匹配详情
     */
    record RuleMatchDetail(
        String ruleId,
        String ruleDescription,
        String fieldName,
        String operator,
        String expectedValue,
        String actualValue,
        boolean matched,
        String message
    ) {}
    
    /**
     * 规则测试详情
     */
    record RuleTestDetail(
        String ruleId,
        String ruleDescription,
        boolean passed,
        String reason,
        String suggestion
    ) {}
}
