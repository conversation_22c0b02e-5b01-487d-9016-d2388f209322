package com.geeksec.nta.alarm.dto.subscription;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 订阅规则 DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionRuleDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 字段名称
     */
    @NotBlank(message = "字段名称不能为空")
    private String fieldName;
    
    /**
     * 操作符
     */
    @NotNull(message = "操作符不能为空")
    private OperatorType operator;
    
    /**
     * 期望值
     */
    @NotBlank(message = "期望值不能为空")
    private String expectedValue;
    
    /**
     * 是否忽略大小写
     */
    private Boolean ignoreCase = false;
    
    /**
     * 操作符类型枚举
     */
    public enum OperatorType {
        /** 等于 */
        EQUALS,
        /** 不等于 */
        NOT_EQUALS,
        /** 包含 */
        CONTAINS,
        /** 不包含 */
        NOT_CONTAINS,
        /** 正则匹配 */
        REGEX,
        /** 大于 */
        GREATER_THAN,
        /** 小于 */
        LESS_THAN,
        /** 大于等于 */
        GREATER_THAN_OR_EQUAL,
        /** 小于等于 */
        LESS_THAN_OR_EQUAL,
        /** 在列表中 */
        IN,
        /** 不在列表中 */
        NOT_IN
    }
    
    /**
     * 创建等于规则
     */
    public static SubscriptionRuleDto createEqualsRule(String fieldName, String expectedValue) {
        return SubscriptionRuleDto.builder()
                .fieldName(fieldName)
                .operator(OperatorType.EQUALS)
                .expectedValue(expectedValue)
                .ignoreCase(false)
                .build();
    }
    
    /**
     * 创建包含规则
     */
    public static SubscriptionRuleDto createContainsRule(String fieldName, String expectedValue) {
        return SubscriptionRuleDto.builder()
                .fieldName(fieldName)
                .operator(OperatorType.CONTAINS)
                .expectedValue(expectedValue)
                .ignoreCase(true)
                .build();
    }
    
    /**
     * 创建正则匹配规则
     */
    public static SubscriptionRuleDto createRegexRule(String fieldName, String pattern) {
        return SubscriptionRuleDto.builder()
                .fieldName(fieldName)
                .operator(OperatorType.REGEX)
                .expectedValue(pattern)
                .ignoreCase(false)
                .build();
    }
    
    /**
     * 创建IN规则
     */
    public static SubscriptionRuleDto createInRule(String fieldName, String values) {
        return SubscriptionRuleDto.builder()
                .fieldName(fieldName)
                .operator(OperatorType.IN)
                .expectedValue(values)
                .ignoreCase(false)
                .build();
    }
}
