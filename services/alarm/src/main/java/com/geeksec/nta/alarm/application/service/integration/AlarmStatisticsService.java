package com.geeksec.nta.alarm.application.service.integration;

import com.geeksec.nta.alarm.application.query.AlarmStatisticsQuery;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmStatisticsResponse;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmTrendResponse;
import com.geeksec.nta.alarm.interfaces.dto.response.AttackChainResponse;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警统计应用服务
 * 负责处理所有告警统计分析相关的业务逻辑
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmStatisticsService {
    
    /**
     * 获取告警统计信息
     * 包括各级别告警数量、攻击者数量、受害者数量等
     * 
     * @param query 统计查询条件
     * @return 统计结果
     */
    AlarmStatisticsResponse getAlarmStatistics(AlarmStatisticsQuery query);
    
    /**
     * 获取告警趋势分析
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param granularity 时间粒度（小时、天、周、月）
     * @return 趋势分析结果
     */
    AlarmTrendResponse getAlarmTrend(LocalDateTime startTime, LocalDateTime endTime, String granularity);
    
    /**
     * 获取攻击链统计
     * 
     * @param query 查询条件
     * @return 攻击链统计结果
     */
    List<AttackChainResponse> getAttackChainStatistics(AlarmStatisticsQuery query);
    
    /**
     * 获取TOP攻击者统计
     * 
     * @param query 查询条件
     * @param limit 返回数量限制
     * @return TOP攻击者列表
     */
    List<AttackerStatistics> getTopAttackers(AlarmStatisticsQuery query, int limit);
    
    /**
     * 获取TOP受害者统计
     * 
     * @param query 查询条件
     * @param limit 返回数量限制
     * @return TOP受害者列表
     */
    List<VictimStatistics> getTopVictims(AlarmStatisticsQuery query, int limit);
    
    /**
     * 获取告警类型分布统计
     * 
     * @param query 查询条件
     * @return 类型分布统计
     */
    List<AlarmTypeStatistics> getAlarmTypeDistribution(AlarmStatisticsQuery query);
    
    /**
     * 获取告警严重程度分布统计
     * 
     * @param query 查询条件
     * @return 严重程度分布统计
     */
    List<SeverityStatistics> getSeverityDistribution(AlarmStatisticsQuery query);
    
    /**
     * 攻击者统计信息
     */
    record AttackerStatistics(
        String attackerIp,
        long alarmCount,
        long victimCount,
        String mostFrequentAlarmType
    ) {}
    
    /**
     * 受害者统计信息
     */
    record VictimStatistics(
        String victimIp,
        long alarmCount,
        long attackerCount,
        String mostFrequentAlarmType
    ) {}
    
    /**
     * 告警类型统计信息
     */
    record AlarmTypeStatistics(
        String alarmType,
        String alarmTypeName,
        long count,
        double percentage
    ) {}
    
    /**
     * 严重程度统计信息
     */
    record SeverityStatistics(
        String severity,
        String severityName,
        long count,
        double percentage
    ) {}
}
