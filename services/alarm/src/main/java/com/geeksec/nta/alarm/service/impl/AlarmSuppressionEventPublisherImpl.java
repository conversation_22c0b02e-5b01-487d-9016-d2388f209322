package com.geeksec.nta.alarm.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.nta.alarm.config.AlarmSuppressionKafkaConfig;
import com.geeksec.nta.alarm.entity.AlarmSuppression;
import com.geeksec.nta.alarm.service.AlarmSuppressionEventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 告警抑制规则事件发布服务实现
 * 负责将抑制规则变更事件发布到Kafka消息队列
 *
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlarmSuppressionEventPublisherImpl implements AlarmSuppressionEventPublisher {

    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;
    private final AlarmSuppressionKafkaConfig kafkaConfig;

    @Override
    public void publishSuppressionAdded(String victim, String attacker, String label) {
        log.debug("发布抑制规则添加事件: victim={}, attacker={}, label={}", victim, attacker, label);

        try {
            Map<String, Object> message = createChangeMessage("ADD", victim, attacker, label);
            sendKafkaMessage(message);

            log.info("成功发布抑制规则添加事件: victim={}, attacker={}, label={}", victim, attacker, label);
        } catch (Exception e) {
            log.error("发布抑制规则添加事件失败: victim={}, attacker={}, label={}", victim, attacker, label, e);
        }
    }

    @Override
    public void publishSuppressionRemoved(String victim, String attacker, String label) {
        log.debug("发布抑制规则移除事件: victim={}, attacker={}, label={}", victim, attacker, label);

        try {
            Map<String, Object> message = createChangeMessage("REMOVE", victim, attacker, label);
            sendKafkaMessage(message);

            log.info("成功发布抑制规则移除事件: victim={}, attacker={}, label={}", victim, attacker, label);
        } catch (Exception e) {
            log.error("发布抑制规则移除事件失败: victim={}, attacker={}, label={}", victim, attacker, label, e);
        }
    }

    @Override
    public void publishBatchSuppressionAdded(List<AlarmSuppression> suppressionItems) {
        log.debug("发布批量抑制规则添加事件，数量: {}", suppressionItems.size());

        for (AlarmSuppression suppression : suppressionItems) {
            publishSuppressionAdded(suppression.getVictim(), suppression.getAttacker(), suppression.getLabel());
        }

        log.info("成功发布批量抑制规则添加事件，数量: {}", suppressionItems.size());
    }

    @Override
    public void publishBatchSuppressionRemoved(String victim, String attacker, String label, int count) {
        log.debug("批量抑制规则移除，将逐个发布移除事件: victim={}, attacker={}, label={}, count={}",
                victim, attacker, label, count);

        // 注意：由于只支持ADD和REMOVE操作，批量删除需要在服务端处理
        // 这里只记录日志，实际的删除事件应该由服务端逐个发布
        log.info("批量抑制规则移除操作已记录，删除数量: {}", count);
    }

    /**
     * 创建变更消息
     */
    private Map<String, Object> createChangeMessage(String operation, String victim, String attacker, String label) {
        Map<String, Object> message = new HashMap<>(8);
        message.put("operation", operation);
        message.put("victim", victim);
        message.put("attacker", attacker);
        message.put("label", label);
        message.put("timestamp", System.currentTimeMillis());
        message.put("eventTime", LocalDateTime.now().toString());
        return message;
    }



    /**
     * 发送Kafka消息
     */
    private void sendKafkaMessage(Map<String, Object> message) {
        try {
            String messageJson = objectMapper.writeValueAsString(message);
            String key = buildMessageKey(message);
            
            String topic = kafkaConfig.getTopic();
            kafkaTemplate.send(topic, key, messageJson)
                    .whenComplete((result, ex) -> {
                        if (ex == null) {
                            log.debug("抑制规则变更消息发送成功: topic={}, partition={}, offset={}",
                                    topic,
                                    result.getRecordMetadata().partition(),
                                    result.getRecordMetadata().offset());
                        } else {
                            log.error("抑制规则变更消息发送失败: topic={}, message={}",
                                    topic, messageJson, ex);
                        }
                    });
            
        } catch (Exception e) {
            log.error("序列化抑制规则变更消息失败", e);
            throw new RuntimeException("发送抑制规则变更消息失败", e);
        }
    }

    /**
     * 构建消息键
     */
    private String buildMessageKey(Map<String, Object> message) {
        String victim = (String) message.get("victim");
        String attacker = (String) message.get("attacker");
        String label = (String) message.get("label");
        return String.format("%s|%s|%s", victim, attacker, label);
    }
}
