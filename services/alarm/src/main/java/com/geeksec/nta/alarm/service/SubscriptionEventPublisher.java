package com.geeksec.nta.alarm.service;

import com.geeksec.nta.alarm.entity.AlarmSubscription;

/**
 * 订阅事件发布服务接口
 * 负责向消息队列发送订阅变更事件
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface SubscriptionEventPublisher {

    /**
     * 发布订阅创建事件
     *
     * @param subscription 订阅对象
     * @param operatedBy 操作人
     */
    void publishSubscriptionCreated(AlarmSubscription subscription, String operatedBy);

    /**
     * 发布订阅更新事件
     *
     * @param subscription 订阅对象
     * @param operatedBy 操作人
     */
    void publishSubscriptionUpdated(AlarmSubscription subscription, String operatedBy);

    /**
     * 发布订阅删除事件
     *
     * @param subscriptionId 订阅ID
     * @param userId 用户ID
     * @param operatedBy 操作人
     */
    void publishSubscriptionDeleted(String subscriptionId, String userId, String operatedBy);

    /**
     * 发布订阅状态变更事件
     *
     * @param subscription 订阅对象
     * @param enabled 是否启用
     * @param operatedBy 操作人
     */
    void publishSubscriptionStatusChanged(AlarmSubscription subscription, boolean enabled, String operatedBy);

    /**
     * 获取订阅变更主题名称
     *
     * @return 主题名称
     */
    String getSubscriptionChangesTopic();
}
