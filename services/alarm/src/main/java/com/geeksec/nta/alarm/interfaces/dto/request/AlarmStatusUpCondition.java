package com.geeksec.nta.alarm.dto.condition;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


@Data
public class AlarmStatusUpCondition {

    /**
     * ES ID
     */
    @JsonProperty("id")
    private String id;

    /**
     * 任务号
     */
    @JsonProperty("task_id")
    private Integer taskId;

    /**
     * 告警状态 0 未处理 1 确认 2 误报
     */
    @JsonProperty("alarm_status")
    private Integer alarmStatus;


}
