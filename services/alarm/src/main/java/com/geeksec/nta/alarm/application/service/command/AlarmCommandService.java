package com.geeksec.nta.alarm.application.service.command;

import com.geeksec.nta.alarm.application.command.CreateAlarmCommand;
import com.geeksec.nta.alarm.application.command.DeleteAlarmCommand;
import com.geeksec.nta.alarm.application.command.UpdateAlarmStatusCommand;
import com.geeksec.nta.alarm.domain.valueobject.AlarmId;

import java.util.List;

/**
 * 告警命令应用服务
 * 负责处理所有告警状态变更相关的业务逻辑
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmCommandService {
    
    /**
     * 创建告警
     * 
     * @param command 创建命令
     * @return 告警ID
     */
    AlarmId createAlarm(CreateAlarmCommand command);
    
    /**
     * 更新告警状态
     * 
     * @param command 更新命令
     * @return 是否成功
     */
    boolean updateAlarmStatus(UpdateAlarmStatusCommand command);
    
    /**
     * 批量更新告警状态
     * 
     * @param commands 更新命令列表
     * @return 成功更新的数量
     */
    int batchUpdateAlarmStatus(List<UpdateAlarmStatusCommand> commands);
    
    /**
     * 删除告警
     * 
     * @param command 删除命令
     * @return 是否成功
     */
    boolean deleteAlarm(DeleteAlarmCommand command);
    
    /**
     * 批量删除告警
     * 
     * @param commands 删除命令列表
     * @return 成功删除的数量
     */
    int batchDeleteAlarms(List<DeleteAlarmCommand> commands);
    
    /**
     * 删除所有告警
     * 
     * @return 删除的数量
     */
    long deleteAllAlarms();
    
    /**
     * 归档过期告警
     * 
     * @param daysToKeep 保留天数
     * @return 归档的数量
     */
    long archiveExpiredAlarms(int daysToKeep);
}
