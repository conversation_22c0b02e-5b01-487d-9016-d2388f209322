package com.geeksec.nta.alarm.service.impl;

import com.alibaba.fastjson2.JSON;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.dto.condition.AlarmCommonCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmListCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmStatusUpCondition;
import com.geeksec.nta.alarm.mapper.AlarmMapper;
import com.geeksec.nta.alarm.service.AlarmService;
import com.geeksec.nta.alarm.vo.AlarmTargetAggVo;
import com.geeksec.nta.alarm.vo.AlarmTypeAggVo;
import com.geeksec.nta.alarm.vo.KnowledgeAlarmVo;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.CharsetUtil;
import com.google.common.collect.Lists;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 告警服务实现类
 * 负责告警的主要业务功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AlarmServiceImpl implements AlarmService, CommandLineRunner {
    
    private final AlarmMapper alarmMapper;
    
    @Override
    public void run(String... args) throws Exception {
        // 系统启动时自动初始化知识库
        log.info("系统启动，开始初始化告警知识库");
        initKnowledgeType();
    }
    
    // ==================== 告警数据管理 ====================
    
    @Override
    public PageResultVo<Map<String, Object>> getAlarmList(AlarmListCondition condition) {
        log.info("获取告警列表: condition={}", condition);
        
        try {
            // 获取总数
            long total = alarmMapper.countAlarms(condition);
            
            if (total == 0) {
                return PageResultVo.<Map<String, Object>>builder()
                        .records(List.of())
                        .total(0L)
                        .current(condition.getCurrentPage())
                        .size(condition.getPageSize())
                        .build();
            }
            
            // 获取分页数据
            List<Map<String, Object>> records = alarmMapper.getAlarmList(condition);
            
            return PageResultVo.<Map<String, Object>>builder()
                    .records(records)
                    .total(total)
                    .current(condition.getCurrentPage())
                    .size(condition.getPageSize())
                    .build();
                    
        } catch (Exception e) {
            log.error("获取告警列表失败", e);
            throw new RuntimeException("获取告警列表失败", e);
        }
    }
    
    @Override
    public Map<String, Object> getAlarmDetail(String esIndex, String alarmId) {
        log.info("获取告警详情: esIndex={}, alarmId={}", esIndex, alarmId);
        
        try {
            Map<String, Object> alarmDetail = alarmMapper.getAlarmDetail(esIndex, alarmId);
            
            if (alarmDetail == null || alarmDetail.isEmpty()) {
                log.warn("未找到告警详情: esIndex={}, alarmId={}", esIndex, alarmId);
                throw new RuntimeException("告警详情不存在");
            }
            
            return alarmDetail;
            
        } catch (Exception e) {
            log.error("获取告警详情失败: esIndex={}, alarmId={}", esIndex, alarmId, e);
            throw new RuntimeException("获取告警详情失败", e);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateAlarmStatus(AlarmStatusUpCondition condition) {
        log.info("更新告警状态: condition={}", condition);
        
        try {
            int updatedCount = alarmMapper.updateAlarmStatus(condition);
            
            if (updatedCount > 0) {
                log.info("成功更新{}条告警状态", updatedCount);
                return "更新告警状态成功";
            } else {
                log.warn("未找到需要更新的告警记录");
                return "未找到需要更新的告警记录";
            }
            
        } catch (Exception e) {
            log.error("更新告警状态失败", e);
            throw new RuntimeException("更新告警状态失败", e);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long deleteAlarms(Map<Integer, List<String>> condition) {
        log.info("删除告警文档: condition={}", condition);
        
        try {
            // 收集所有需要删除的告警ID
            List<String> allIdsToDelete = condition.values().stream()
                    .flatMap(List::stream)
                    .distinct()
                    .toList();
            
            if (allIdsToDelete.isEmpty()) {
                log.warn("没有需要删除的告警ID");
                return 0L;
            }
            
            // 批量删除相关表数据
            alarmMapper.deleteFromAlarmSourcesByIds(allIdsToDelete);
            alarmMapper.deleteFromAlarmAttackersByIds(allIdsToDelete);
            alarmMapper.deleteFromAlarmReasonsByIds(allIdsToDelete);
            alarmMapper.deleteFromAlarmTargetsByIds(allIdsToDelete);
            alarmMapper.deleteFromAlarmVictimsByIds(allIdsToDelete);
            
            // 最后删除主表数据
            alarmMapper.deleteFromAlarmsByIds(allIdsToDelete);
            
            log.info("成功删除{}条告警记录", allIdsToDelete.size());
            return (long) allIdsToDelete.size();
            
        } catch (Exception e) {
            log.error("删除告警文档失败", e);
            throw new RuntimeException("删除告警文档失败", e);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deleteAllAlarms() {
        log.info("开始删除所有告警数据");
        
        try {
            // 按照子表->主表的顺序清空数据
            alarmMapper.truncateAlarmSources();
            alarmMapper.truncateAlarmAttackers();
            alarmMapper.truncateAlarmVictims();
            alarmMapper.truncateAlarmTargets();
            alarmMapper.truncateAlarmReasons();
            alarmMapper.truncateAlarms();
            
            log.info("所有告警数据删除成功");
            return "删除所有告警成功";
            
        } catch (Exception e) {
            log.error("删除所有告警数据失败", e);
            throw new RuntimeException("删除所有告警数据失败", e);
        }
    }
    
    // ==================== 告警统计分析 ====================
    
    @Override
    public AlarmTargetAggVo getAlarmTargetAgg(AlarmCommonCondition condition) {
        log.info("获取告警指标统计信息: condition={}", condition);
        
        try {
            return alarmMapper.getAlarmTargetAgg(condition);
        } catch (Exception e) {
            log.error("获取告警指标统计信息失败", e);
            throw new RuntimeException("获取告警指标统计信息失败", e);
        }
    }
    
    @Override
    public List<AlarmTypeAggVo.AttackChain> getAttackChainAggr(AlarmCommonCondition condition) {
        log.info("获取告警攻击链路统计: condition={}", condition);
        
        try {
            return alarmMapper.getAttackChainAggr(condition);
        } catch (Exception e) {
            log.error("获取告警攻击链路统计失败", e);
            throw new RuntimeException("获取告警攻击链路统计失败", e);
        }
    }
    
    // ==================== 告警知识库 ====================
    
    @Override
    public void initKnowledgeType() {
        log.info("开始初始化告警知识库和采集规则字典");
        
        try {
            // 初始化告警知识库字典
            long knowledgeCount = alarmMapper.countKnowledgeAlarms();
            if (knowledgeCount == 0) {
                log.info("告警知识库为空，开始初始化默认数据");
                alarmMapper.insertDefaultKnowledgeData();
            }
            
            // 初始化采集规则字典
            long ruleCount = alarmMapper.countCollectionRules();
            if (ruleCount == 0) {
                log.info("采集规则字典为空，开始初始化默认数据");
                alarmMapper.insertDefaultCollectionRules();
            }
            
            log.info("告警知识库初始化完成");
            
        } catch (Exception e) {
            log.error("初始化告警知识库失败", e);
            throw new RuntimeException("初始化告警知识库失败", e);
        }
    }
    
    @Override
    public List<KnowledgeAlarmVo> getKnowledgeAlarmList() {
        log.info("获取告警知识库全量列表");
        
        try {
            List<KnowledgeAlarmVo> knowledgeList = alarmMapper.getKnowledgeAlarmList();
            return knowledgeList != null ? knowledgeList : List.of();
            
        } catch (Exception e) {
            log.error("获取告警知识库列表失败", e);
            throw new RuntimeException("获取告警知识库列表失败", e);
        }
    }
    
    // ==================== 告警导出 ====================
    
    @Override
    public ApiResponse<String> exportAlarmToCsv(AlarmListCondition condition, HttpServletResponse response) {
        log.info("导出CSV格式请求: {}", condition);
        
        try {
            PageResultVo<Map<String, Object>> resultVo = getAlarmList(condition);
            List<Map<String, Object>> records = resultVo.getRecords();
            
            if (CollUtil.isEmpty(records)) {
                return ApiResponse.success("无告警数据可进行导出");
            }
            
            List<List<String>> rowList = Lists.newArrayListWithCapacity(records.size());
            for (Map<String, Object> record : records) {
                List<String> row = new ArrayList<>();
                row.add(JSON.toJSONString(record));
                rowList.add(row);
            }
            
            // 写数据
            CsvWriter writer = null;
            String fileName = "Alarm_" + System.currentTimeMillis() + ".csv";
            try {
                File csvFile = new File(fileName);
                writer = CsvUtil.getWriter(csvFile, CharsetUtil.CHARSET_UTF_8);
                writer.write(rowList);
                
                // 设置响应头
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                
                // 写入响应流
                try (var fis = new java.io.FileInputStream(csvFile);
                     var os = response.getOutputStream()) {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = fis.read(buffer)) != -1) {
                        os.write(buffer, 0, bytesRead);
                    }
                    os.flush();
                }
                
                // 删除临时文件
                if (csvFile.exists()) {
                    csvFile.delete();
                }
                
                return ApiResponse.success("CSV导出成功");
                
            } finally {
                if (writer != null) {
                    writer.close();
                }
            }
            
        } catch (Exception e) {
            log.error("导出CSV格式失败", e);
            return ApiResponse.error("导出CSV格式失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiResponse prepareAlarmSessionPcap(Integer userId, List<String> alarmSessionList, String alarmType, Long alarmTime) {
        log.info("准备告警会话PCAP下载: userId={}, sessionCount={}, alarmType={}, alarmTime={}", 
                userId, alarmSessionList != null ? alarmSessionList.size() : 0, alarmType, alarmTime);
        
        try {
            // TODO: 实现PCAP下载准备逻辑
            String taskId = "pcap_task_" + System.currentTimeMillis() + "_" + userId;
            
            return ApiResponse.success(Map.of(
                    "taskId", taskId,
                    "sessionCount", alarmSessionList.size(),
                    "message", "PCAP下载任务已准备完成"
            ));
            
        } catch (Exception e) {
            log.error("准备告警会话PCAP下载失败", e);
            return ApiResponse.error("准备PCAP下载失败: " + e.getMessage());
        }
    }
}
