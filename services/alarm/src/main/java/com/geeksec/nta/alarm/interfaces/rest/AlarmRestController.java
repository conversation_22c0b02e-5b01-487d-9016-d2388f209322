package com.geeksec.nta.alarm.interfaces.rest;

import com.geeksec.nta.alarm.application.service.AlarmQueryService;
import com.geeksec.nta.alarm.application.service.AlarmCommandService;
import com.geeksec.nta.alarm.application.service.AlarmStatisticsService;
import com.geeksec.nta.alarm.application.service.AlarmExportService;
import com.geeksec.nta.alarm.interfaces.dto.common.ApiResult;
import com.geeksec.nta.alarm.interfaces.dto.common.PageResult;
import com.geeksec.nta.alarm.interfaces.dto.request.AlarmQueryRequest;
import com.geeksec.nta.alarm.interfaces.dto.request.AlarmStatusUpdateRequest;
import com.geeksec.nta.alarm.interfaces.dto.request.AlarmExportRequest;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmDetailResponse;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmListResponse;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmStatisticsResponse;
import com.geeksec.nta.alarm.interfaces.dto.response.ExportTaskResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 告警REST控制器
 * 负责告警资源的RESTful API
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/alarms")
@RequiredArgsConstructor
@Tag(name = "告警管理", description = "告警资源的CRUD操作和查询")
public class AlarmRestController {
    
    private final AlarmQueryService alarmQueryService;
    private final AlarmCommandService alarmCommandService;
    private final AlarmStatisticsService alarmStatisticsService;
    private final AlarmExportService alarmExportService;
    
    // ==================== 告警查询操作 ====================
    
    /**
     * 查询告警列表
     */
    @GetMapping
    @Operation(summary = "查询告警列表", description = "分页查询告警列表，支持多种过滤条件")
    public ApiResult<PageResult<AlarmListResponse>> getAlarms(
            @Valid @ModelAttribute AlarmQueryRequest request) {
        
        log.info("查询告警列表: {}", request);
        
        var query = request.toQuery();
        var result = alarmQueryService.queryAlarmList(query);
        var pageResult = PageResult.of(result.getRecords(), result.getTotal(), 
                                     result.getCurrent(), result.getSize());
        
        return ApiResult.success(pageResult, "查询成功");
    }
    
    /**
     * 查询告警详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询告警详情", description = "根据告警ID查询详细信息")
    public ApiResult<AlarmDetailResponse> getAlarm(
            @Parameter(description = "告警ID") @PathVariable String id) {
        
        log.info("查询告警详情: {}", id);
        
        var alarmId = new com.geeksec.nta.alarm.domain.valueobject.AlarmId(id);
        var alarm = alarmQueryService.queryAlarmById(alarmId);
        
        return ApiResult.success(alarm, "查询成功");
    }
    
    /**
     * 检查告警是否存在
     */
    @GetMapping("/{id}/exists")
    @Operation(summary = "检查告警是否存在", description = "检查指定ID的告警是否存在")
    public ApiResult<Boolean> existsAlarm(
            @Parameter(description = "告警ID") @PathVariable String id) {
        
        log.info("检查告警是否存在: {}", id);
        
        var alarmId = new com.geeksec.nta.alarm.domain.valueobject.AlarmId(id);
        boolean exists = alarmQueryService.existsById(alarmId);
        
        return ApiResult.success(exists);
    }
    
    // ==================== 告警状态管理 ====================
    
    /**
     * 更新告警状态
     */
    @PatchMapping("/{id}/status")
    @Operation(summary = "更新告警状态", description = "更新指定告警的处理状态")
    public ApiResult<Void> updateAlarmStatus(
            @Parameter(description = "告警ID") @PathVariable String id,
            @Valid @RequestBody AlarmStatusUpdateRequest request) {
        
        log.info("更新告警状态: id={}, request={}", id, request);
        
        var command = request.toCommand(List.of(id));
        boolean success = alarmCommandService.updateAlarmStatus(command);
        
        if (success) {
            return ApiResult.success("状态更新成功");
        } else {
            return ApiResult.businessError("状态更新失败");
        }
    }
    
    /**
     * 批量更新告警状态
     */
    @PatchMapping("/status")
    @Operation(summary = "批量更新告警状态", description = "批量更新多个告警的处理状态")
    public ApiResult<Integer> batchUpdateAlarmStatus(
            @Valid @RequestBody AlarmStatusUpdateRequest request) {
        
        log.info("批量更新告警状态: {}", request);
        
        var commands = request.toCommands();
        int updatedCount = alarmCommandService.batchUpdateAlarmStatus(commands);
        
        return ApiResult.success(updatedCount, String.format("成功更新%d个告警状态", updatedCount));
    }
    
    // ==================== 告警删除操作 ====================
    
    /**
     * 删除告警
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除告警", description = "删除指定的告警记录")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public ApiResult<Void> deleteAlarm(
            @Parameter(description = "告警ID") @PathVariable String id,
            @Parameter(description = "操作人") @RequestParam String operator) {
        
        log.info("删除告警: id={}, operator={}", id, operator);
        
        var command = new com.geeksec.nta.alarm.application.command.DeleteAlarmCommand(
                new com.geeksec.nta.alarm.domain.valueobject.AlarmId(id), 
                operator, 
                "手动删除"
        );
        
        boolean success = alarmCommandService.deleteAlarm(command);
        
        if (success) {
            return ApiResult.success("删除成功");
        } else {
            return ApiResult.businessError("删除失败");
        }
    }
    
    /**
     * 批量删除告警
     */
    @DeleteMapping
    @Operation(summary = "批量删除告警", description = "批量删除多个告警记录")
    public ApiResult<Integer> batchDeleteAlarms(
            @Parameter(description = "告警ID列表") @RequestParam List<String> ids,
            @Parameter(description = "操作人") @RequestParam String operator) {
        
        log.info("批量删除告警: ids={}, operator={}", ids, operator);
        
        var commands = ids.stream()
                .map(id -> new com.geeksec.nta.alarm.application.command.DeleteAlarmCommand(
                        new com.geeksec.nta.alarm.domain.valueobject.AlarmId(id),
                        operator,
                        "批量删除"
                ))
                .toList();
        
        int deletedCount = alarmCommandService.batchDeleteAlarms(commands);
        
        return ApiResult.success(deletedCount, String.format("成功删除%d个告警", deletedCount));
    }
    
    /**
     * 删除所有告警
     */
    @DeleteMapping("/all")
    @Operation(summary = "删除所有告警", description = "清空所有告警数据，谨慎操作")
    public ApiResult<Long> deleteAllAlarms(
            @Parameter(description = "操作人") @RequestParam String operator,
            @Parameter(description = "确认标识") @RequestParam String confirm) {
        
        log.warn("删除所有告警数据: operator={}, confirm={}", operator, confirm);
        
        if (!"DELETE_ALL_ALARMS".equals(confirm)) {
            return ApiResult.businessError("确认标识不正确");
        }
        
        long deletedCount = alarmCommandService.deleteAllAlarms();
        
        return ApiResult.success(deletedCount, String.format("成功删除%d个告警", deletedCount));
    }
}
