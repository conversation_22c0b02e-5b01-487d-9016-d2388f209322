package com.geeksec.nta.alarm.domain.event;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 订阅变更事件
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionChangeEvent implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 事件ID
     */
    private String eventId;
    
    /**
     * 订阅ID
     */
    private String subscriptionId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 事件类型
     */
    private EventType eventType;
    
    /**
     * 事件时间
     */
    private LocalDateTime eventTime;
    
    /**
     * 订阅数据（JSON格式）
     */
    private String subscriptionData;
    
    /**
     * 操作人
     */
    private String operatedBy;
    
    /**
     * 事件描述
     */
    private String description;
    
    /**
     * 事件类型枚举
     */
    public enum EventType {
        /** 创建订阅 */
        CREATED,
        /** 更新订阅 */
        UPDATED,
        /** 删除订阅 */
        DELETED,
        /** 启用订阅 */
        ENABLED,
        /** 禁用订阅 */
        DISABLED
    }
    
    /**
     * 创建创建事件
     */
    public static SubscriptionChangeEvent createCreatedEvent(String subscriptionId, String userId, 
                                                           String subscriptionData, String operatedBy) {
        return SubscriptionChangeEvent.builder()
                .eventId(generateEventId())
                .subscriptionId(subscriptionId)
                .userId(userId)
                .eventType(EventType.CREATED)
                .eventTime(LocalDateTime.now())
                .subscriptionData(subscriptionData)
                .operatedBy(operatedBy)
                .description("创建订阅")
                .build();
    }
    
    /**
     * 创建更新事件
     */
    public static SubscriptionChangeEvent createUpdatedEvent(String subscriptionId, String userId,
                                                           String subscriptionData, String operatedBy) {
        return SubscriptionChangeEvent.builder()
                .eventId(generateEventId())
                .subscriptionId(subscriptionId)
                .userId(userId)
                .eventType(EventType.UPDATED)
                .eventTime(LocalDateTime.now())
                .subscriptionData(subscriptionData)
                .operatedBy(operatedBy)
                .description("更新订阅")
                .build();
    }
    
    /**
     * 创建删除事件
     */
    public static SubscriptionChangeEvent createDeletedEvent(String subscriptionId, String userId, String operatedBy) {
        return SubscriptionChangeEvent.builder()
                .eventId(generateEventId())
                .subscriptionId(subscriptionId)
                .userId(userId)
                .eventType(EventType.DELETED)
                .eventTime(LocalDateTime.now())
                .operatedBy(operatedBy)
                .description("删除订阅")
                .build();
    }
    
    /**
     * 创建启用/禁用事件
     */
    public static SubscriptionChangeEvent createStatusChangeEvent(String subscriptionId, String userId,
                                                                String subscriptionData, boolean enabled, String operatedBy) {
        EventType eventType = enabled ? EventType.ENABLED : EventType.DISABLED;
        String description = enabled ? "启用订阅" : "禁用订阅";
        
        return SubscriptionChangeEvent.builder()
                .eventId(generateEventId())
                .subscriptionId(subscriptionId)
                .userId(userId)
                .eventType(eventType)
                .eventTime(LocalDateTime.now())
                .subscriptionData(subscriptionData)
                .operatedBy(operatedBy)
                .description(description)
                .build();
    }
    
    /**
     * 生成事件ID
     */
    private static String generateEventId() {
        return "sub_event_" + System.currentTimeMillis() + "_" + 
               (int)(Math.random() * 1000);
    }
}
