package com.geeksec.nta.alarm.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class AlarmTypeAggVo {


    /** 告警类型 */
    @JsonProperty("alarm_type")
    private String alarmType;

    @JsonProperty("alarm_cnt")
    private long alarmCnt;

    /** 告警阶段 */
    @JsonProperty("attack_chain_list")
    private List<AttackChain> attackChainList;

    @Data
    public static class AttackChain{
        @JsonProperty("attack_chain_name")
        private String attackChainName;

        @JsonProperty("attack_chain_cnt")
        private long attackChainCnt;

        @JsonProperty("alarm_knowledge_list")
        private List<AlarmKnowledge> alarmKnowledgeList;
    }

    @Data
    public static class AlarmKnowledge{
        /** 知识库ID */
        @JsonProperty("alarm_knowledge_id")
        private Integer alarmKnowledgeId;

        @JsonProperty("alarm_knowledge_name")
        private String alarmKnowledgeName;

        @JsonProperty("alarm_knowledge_cnt")
        private long alarmKnowledgeCnt;
    }
}
