package com.geeksec.nta.alarm.interfaces.dto.request;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;

/**
 * 频率控制配置 DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FrequencyConfigDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 时间间隔（分钟）
     */
    private Integer intervalMinutes;
    
    /**
     * 最大通知次数（每天）
     */
    private Integer maxNotificationsPerDay;
    
    /**
     * 批量通知阈值
     */
    private Integer batchThreshold;
    
    /**
     * 批量等待时间（分钟）
     */
    private Integer batchWaitMinutes;
    
    /**
     * 创建实时通知配置
     */
    public static FrequencyConfigDto createRealTimeConfig() {
        return FrequencyConfigDto.builder()
                .intervalMinutes(0)
                .maxNotificationsPerDay(1000)
                .build();
    }
    
    /**
     * 创建间隔通知配置
     */
    public static FrequencyConfigDto createIntervalConfig(int intervalMinutes) {
        return FrequencyConfigDto.builder()
                .intervalMinutes(intervalMinutes)
                .maxNotificationsPerDay(100)
                .build();
    }
    
    /**
     * 创建批量通知配置
     */
    public static FrequencyConfigDto createBatchConfig(int batchThreshold, int batchWaitMinutes) {
        return FrequencyConfigDto.builder()
                .batchThreshold(batchThreshold)
                .batchWaitMinutes(batchWaitMinutes)
                .maxNotificationsPerDay(50)
                .build();
    }
}
