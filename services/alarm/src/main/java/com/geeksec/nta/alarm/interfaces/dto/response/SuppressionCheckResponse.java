package com.geeksec.nta.alarm.interfaces.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 抑制规则检查响应 VO
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "抑制规则检查响应")
public class SuppressionCheckResponse {
    
    @Schema(description = "是否应被抑制")
    private Boolean shouldSuppress;
    
    @Schema(description = "匹配的抑制规则类型")
    private String matchedType;
    
    @Schema(description = "匹配的抑制规则值")
    private String matchedValue;
    
    @Schema(description = "检查时间戳")
    private Long timestamp;
    
    /**
     * 创建成功响应（应被抑制）
     */
    public static SuppressionCheckResponse success(String matchedType, String matchedValue) {
        return SuppressionCheckResponse.builder()
                .shouldSuppress(true)
                .matchedType(matchedType)
                .matchedValue(matchedValue)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 创建失败响应（不应被抑制）
     */
    public static SuppressionCheckResponse notSuppressed() {
        return SuppressionCheckResponse.builder()
                .shouldSuppress(false)
                .timestamp(System.currentTimeMillis())
                .build();
    }
}