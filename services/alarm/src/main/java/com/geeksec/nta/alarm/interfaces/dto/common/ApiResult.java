package com.geeksec.nta.alarm.interfaces.dto.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 统一API响应格式
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResult<T> {
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 错误信息
     */
    private ErrorInfo error;
    
    /**
     * 响应时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 请求追踪ID
     */
    private String traceId;
    
    /**
     * 创建成功响应
     */
    public static <T> ApiResult<T> success(T data) {
        return ApiResult.<T>builder()
                .success(true)
                .data(data)
                .message("操作成功")
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建成功响应（带消息）
     */
    public static <T> ApiResult<T> success(T data, String message) {
        return ApiResult.<T>builder()
                .success(true)
                .data(data)
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建成功响应（无数据）
     */
    public static ApiResult<Void> success() {
        return ApiResult.<Void>builder()
                .success(true)
                .message("操作成功")
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建成功响应（无数据，带消息）
     */
    public static ApiResult<Void> success(String message) {
        return ApiResult.<Void>builder()
                .success(true)
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建错误响应
     */
    public static <T> ApiResult<T> error(String code, String message) {
        return ApiResult.<T>builder()
                .success(false)
                .error(ErrorInfo.builder()
                        .code(code)
                        .message(message)
                        .build())
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建错误响应（带详细信息）
     */
    public static <T> ApiResult<T> error(String code, String message, List<ErrorDetail> details) {
        return ApiResult.<T>builder()
                .success(false)
                .error(ErrorInfo.builder()
                        .code(code)
                        .message(message)
                        .details(details)
                        .build())
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 创建验证错误响应
     */
    public static <T> ApiResult<T> validationError(List<ErrorDetail> details) {
        return error("VALIDATION_ERROR", "参数验证失败", details);
    }
    
    /**
     * 创建业务错误响应
     */
    public static <T> ApiResult<T> businessError(String message) {
        return error("BUSINESS_ERROR", message);
    }
    
    /**
     * 创建系统错误响应
     */
    public static <T> ApiResult<T> systemError() {
        return error("SYSTEM_ERROR", "系统内部错误");
    }
    
    /**
     * 创建资源不存在错误响应
     */
    public static <T> ApiResult<T> notFound(String resource) {
        return error("RESOURCE_NOT_FOUND", resource + "不存在");
    }
    
    /**
     * 创建权限不足错误响应
     */
    public static <T> ApiResult<T> forbidden() {
        return error("FORBIDDEN", "权限不足");
    }
    
    /**
     * 创建未认证错误响应
     */
    public static <T> ApiResult<T> unauthorized() {
        return error("UNAUTHORIZED", "未认证");
    }
    
    /**
     * 错误信息
     */
    @Data
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ErrorInfo {
        
        /**
         * 错误代码
         */
        private String code;
        
        /**
         * 错误消息
         */
        private String message;
        
        /**
         * 错误详细信息
         */
        private List<ErrorDetail> details;
    }
    
    /**
     * 错误详细信息
     */
    @Data
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ErrorDetail {
        
        /**
         * 字段名称
         */
        private String field;
        
        /**
         * 错误消息
         */
        private String message;
        
        /**
         * 错误值
         */
        private Object rejectedValue;
    }
}
