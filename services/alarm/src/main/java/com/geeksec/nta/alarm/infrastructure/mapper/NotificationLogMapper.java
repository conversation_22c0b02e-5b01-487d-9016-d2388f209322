package com.geeksec.nta.alarm.infrastructure.mapper;

import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.paginate.Page;
import com.geeksec.nta.alarm.domain.aggregate.NotificationLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 通知发送记录 Mapper
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Mapper
public interface NotificationLogMapper extends BaseMapper<NotificationLog> {
    
    /**
     * 分页查询通知记录
     * 
     * @param page 分页参数
     * @param subscriptionId 订阅ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    Page<NotificationLog> selectWithPage(Page<NotificationLog> page,
                                        @Param("subscriptionId") String subscriptionId,
                                        @Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);
    
    /**
     * 批量插入通知记录
     * 
     * @param logs 通知记录列表
     * @return 插入数量
     */
    int batchInsert(@Param("logs") List<NotificationLog> logs);
    
    /**
     * 查询订阅的通知统计
     * 
     * @param subscriptionId 订阅ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    NotificationStatistics selectStatistics(@Param("subscriptionId") String subscriptionId,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);
    
    /**
     * 通知统计结果
     */
    class NotificationStatistics {
        private Long totalCount;
        private Long successCount;
        private Long failedCount;
        private Double successRate;
        
        // getters and setters
        public Long getTotalCount() { return totalCount; }
        public void setTotalCount(Long totalCount) { this.totalCount = totalCount; }
        
        public Long getSuccessCount() { return successCount; }
        public void setSuccessCount(Long successCount) { this.successCount = successCount; }
        
        public Long getFailedCount() { return failedCount; }
        public void setFailedCount(Long failedCount) { this.failedCount = failedCount; }
        
        public Double getSuccessRate() { return successRate; }
        public void setSuccessRate(Double successRate) { this.successRate = successRate; }
    }
}
