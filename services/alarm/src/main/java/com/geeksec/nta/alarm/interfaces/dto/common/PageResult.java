package com.geeksec.nta.alarm.interfaces.dto.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 分页响应格式
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageResult<T> {
    
    /**
     * 数据记录
     */
    private List<T> records;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 当前页码
     */
    private Integer page;
    
    /**
     * 页大小
     */
    private Integer size;
    
    /**
     * 总页数
     */
    private Integer pages;
    
    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;
    
    /**
     * 是否有下一页
     */
    private Boolean hasNext;
    
    /**
     * 是否为第一页
     */
    private Boolean isFirst;
    
    /**
     * 是否为最后一页
     */
    private Boolean isLast;
    
    /**
     * 创建分页结果
     */
    public static <T> PageResult<T> of(List<T> records, long total, int page, int size) {
        int pages = (int) Math.ceil((double) total / size);
        
        return PageResult.<T>builder()
                .records(records)
                .total(total)
                .page(page)
                .size(size)
                .pages(pages)
                .hasPrevious(page > 1)
                .hasNext(page < pages)
                .isFirst(page == 1)
                .isLast(page == pages || total == 0)
                .build();
    }
    
    /**
     * 创建空分页结果
     */
    public static <T> PageResult<T> empty(int page, int size) {
        return PageResult.<T>builder()
                .records(List.of())
                .total(0L)
                .page(page)
                .size(size)
                .pages(0)
                .hasPrevious(false)
                .hasNext(false)
                .isFirst(true)
                .isLast(true)
                .build();
    }
    
    /**
     * 转换数据类型
     */
    public <R> PageResult<R> map(java.util.function.Function<T, R> mapper) {
        List<R> mappedRecords = records.stream()
                .map(mapper)
                .toList();
        
        return PageResult.<R>builder()
                .records(mappedRecords)
                .total(total)
                .page(page)
                .size(size)
                .pages(pages)
                .hasPrevious(hasPrevious)
                .hasNext(hasNext)
                .isFirst(isFirst)
                .isLast(isLast)
                .build();
    }
}
