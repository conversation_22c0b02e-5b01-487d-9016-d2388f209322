package com.geeksec.nta.alarm.mapper;

import com.geeksec.nta.alarm.dto.condition.AlarmCommonCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmListCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmRoleJudgeCondition;
import com.geeksec.nta.alarm.entity.Alarm;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;
/**
 * 告警Mapper接口
 *
 * 使用 MyBatis-Flex 1.10.9 BaseMapper
 *
 * <AUTHOR>
 * @since 3.0.0
 */


@Mapper
public interface AlarmMapper extends BaseMapper<Alarm> {

    /**
     * 根据通用查询条件，一次性获取所有告警指标的聚合统计信息。
     *
     * @param conditions 一个包含所有查询条件的Map，由DorisQueryHelper生成。
     * @return 一个Map，键是聚合的别名（如 "low_level_count"），值是对应的计数值。
     */
    Map<String, Long> getAlarmAggregations(@Param("conditions") Map<String, Object> conditions);

    @MapKey("attack_chain_name")
    List<Map<String, Object>> getModelAlarmAttackChainAggr(AlarmCommonCondition condition);

    long countAlarmList(Map<String, Object> params);

    List<Map<String, Object>> queryAlarmList(Map<String, Object> params);

    /**
     * 根据告警ID和索引名称查询告警的完整详情。
     * 此方法通过一次JOIN查询整合所有相关信息。
     *
     * @param id 告警的主ID (对应 alarmId)。
     * @param index 告警的索引名称 (对应 esIndex)。
     * @return 包含所有详情的Map，如果未找到则返回null。
     */
    Map<String, Object> getAlarmDetail(@Param("id") String id, @Param("index") String index);

    /**
     * 更新前查找告警源记录。
     * 此操作用于获取必要数据（如下文的攻击链）并确认记录存在。
     *
     * @param id 告警的主ID。
     * @param taskId 与告警关联的任务ID。
     * @return 一个包含 'attack_chain_list' 等字段的Map，如果未找到则返回null。
     */
    Map<String, Object> findSourceForUpdate(@Param("id") String id, @Param("taskId") Integer taskId);

    /**
     * 更新指定告警记录的告警状态。
     *
     * @param id 告警的主ID。
     * @param taskId 与告警关联的任务ID。
     * @param alarmStatus 要设置的新状态。
     * @return 受影响的行数（成功时应为1）。
     */
    int updateAlarmStatus(@Param("id") String id, @Param("taskId") Integer taskId, @Param("alarmStatus") Integer alarmStatus);

    /**
     * 根据复杂的任务ID和告警ID组合条件，查找所有匹配的告警ID。
     *
     * @param map Key为taskId, Value为alarmId列表。
     * @return 符合条件的alarm_id列表。
     */
    List<String> findAlarmIdsForDeletion(@Param("map") Map<Integer, List<String>> map);

    /**
     * 根据ID列表，从所有相关的告警表中批量删除记录。
     * 我们为每个表创建一个方法以保持清晰。
     */
    long deleteFromAlarmsByIds(@Param("ids") List<String> ids);
    long deleteFromAlarmSourcesByIds(@Param("ids") List<String> ids);
    long deleteFromAlarmAttackersByIds(@Param("ids") List<String> ids);
    long deleteFromAlarmReasonsByIds(@Param("ids") List<String> ids);
    long deleteFromAlarmTargetsByIds(@Param("ids") List<String> ids);
    long deleteFromAlarmVictimsByIds(@Param("ids") List<String> ids);


    /**
     * 使用 TRUNCATE 命令清空所有告警相关的表。
     * 为每个表创建一个单独的方法，以保证操作的清晰性。
     */
    void truncateAlarms();
    void truncateAlarmSources();
    void truncateAlarmAttackers();
    void truncateAlarmVictims();
    void truncateAlarmTargets();
    void truncateAlarmReasons();

    /**
     * 根据复杂的告警列表条件，动态查询出所有匹配的告警ID，用于报告导出。
     *
     * @param condition 包含所有筛选和排序条件的对象。
     * @return 符合条件的alarm_id列表。
     */
    List<String> findAlarmIdsForExport(@Param("condition") AlarmListCondition condition);


    /**
     * 根据会话ID列表，批量查询会话的元数据。
     *
     * @param sessionIds 会话ID的列表。
     * @return 一个Map的列表，每个Map代表一条会话的元数据记录。
     */
    List<Map<String, Object>> findSessionMetadataBySessionIds(@Param("sessionIds") List<String> sessionIds);

    /**
     * 根据角色和IP地址，在指定时间范围内查询最新的告警信息。
     * 替代原 createAlarmJudgeGraphByRole 方法中的ES查询。
     *
     * @param ipAddr IP地址
     * @param roles 角色列表 (e.g., "attacker", "victim")
     * @param timeRange 时间范围对象
     * @param limit 查询数量
     * @return 返回一个Map列表，每个Map代表一条告警的源信息(alarm_source)。
     */
    List<Map<String, Object>> findAlarmsByRoleAndIp(
            @Param("ipAddr") String ipAddr,
            @Param("roles") List<String> roles,
            @Param("timeRange") AlarmRoleJudgeCondition.TimeRange timeRange,
            @Param("limit") int limit
    );



}
