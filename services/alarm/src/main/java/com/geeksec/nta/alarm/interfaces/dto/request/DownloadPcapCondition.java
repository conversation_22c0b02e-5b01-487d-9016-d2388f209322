package com.geeksec.nta.alarm.dto.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: GuanHao
 * @Date: 2022/5/16 16:52
 * @Description： <Functions List>
 */
@Data
public class DownloadPcapCondition {

    /**
     * 查询条件
     */
    @JsonProperty("query")
    private AnalysisBaseCondition query;

    /**
     * 前端展示条件
     */
    @JsonProperty("show_query")
    private String showQuery;

    /**
     * 用户名
     */
    @JsonProperty("user_id")
    private Integer userId;

    /**
     * 列信息
     */
    @JsonProperty("session")
    private List<Session> sessionId;

    /**
     * 是否是全量下载
     */
    @JsonProperty("type")
    private Boolean type = false;

    @Data
    public static class Session {

        @JsonProperty("session_id")
        private String sessionId;

        @JsonProperty("task_id")
        private Integer taskId;

        @JsonProperty("batch_id")
        private String batchId;

        @JsonProperty("thread_id")
        private Integer threadId;

        @JsonProperty("start_time")
        private Long startTime;

        @JsonProperty("end_time")
        private Long endTime;

        @JsonProperty("first_proto")
        private Integer firstProto;
    }
}
