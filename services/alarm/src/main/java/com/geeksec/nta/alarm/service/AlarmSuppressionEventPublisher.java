package com.geeksec.nta.alarm.service;

import com.geeksec.nta.alarm.entity.AlarmSuppression;

import java.util.List;

/**
 * 告警抑制规则事件发布服务接口
 * 负责发布抑制规则变更事件到消息队列
 *
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
public interface AlarmSuppressionEventPublisher {

    /**
     * 发布抑制规则添加事件
     *
     * @param victim 受害者IP
     * @param attacker 攻击者IP
     * @param label 告警标签
     */
    void publishSuppressionAdded(String victim, String attacker, String label);

    /**
     * 发布抑制规则移除事件
     *
     * @param victim 受害者IP
     * @param attacker 攻击者IP
     * @param label 告警标签
     */
    void publishSuppressionRemoved(String victim, String attacker, String label);

    /**
     * 发布批量抑制规则添加事件
     *
     * @param suppressionItems 抑制规则项列表
     */
    void publishBatchSuppressionAdded(List<AlarmSuppression> suppressionItems);

    /**
     * 发布批量抑制规则移除事件
     *
     * @param victim 受害者IP（可选）
     * @param attacker 攻击者IP（可选）
     * @param label 告警标签（可选）
     * @param count 移除数量
     */
    void publishBatchSuppressionRemoved(String victim, String attacker, String label, int count);
}
