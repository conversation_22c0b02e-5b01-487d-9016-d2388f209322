package com.geeksec.nta.alarm.application.service.impl;

import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.application.query.AlarmDetailQuery;
import com.geeksec.nta.alarm.application.query.AlarmListQuery;
import com.geeksec.nta.alarm.application.service.AlarmQueryService;
import com.geeksec.nta.alarm.domain.aggregate.Alarm;
import com.geeksec.nta.alarm.domain.repository.AlarmRepository;
import com.geeksec.nta.alarm.domain.valueobject.AlarmId;
import com.geeksec.nta.alarm.infrastructure.converter.AlarmConverter;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmDetailResponse;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 告警查询应用服务实现
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class AlarmQueryServiceImpl implements AlarmQueryService {
    
    private final AlarmRepository alarmRepository;
    private final AlarmConverter alarmConverter;
    
    @Override
    public PageResultVo<AlarmListResponse> queryAlarmList(AlarmListQuery query) {
        log.info("查询告警列表: {}", query);
        
        // 验证查询参数
        query.validate();
        
        try {
            // 查询总数
            long total = alarmRepository.countByQuery(query);
            
            if (total == 0) {
                return PageResultVo.<AlarmListResponse>builder()
                        .records(List.of())
                        .total(0L)
                        .current(query.getPage())
                        .size(query.getSize())
                        .build();
            }
            
            // 查询分页数据
            List<Alarm> alarms = alarmRepository.findByQuery(query);
            List<AlarmListResponse> responses = alarmConverter.toListResponses(alarms);
            
            return PageResultVo.<AlarmListResponse>builder()
                    .records(responses)
                    .total(total)
                    .current(query.getPage())
                    .size(query.getSize())
                    .build();
                    
        } catch (Exception e) {
            log.error("查询告警列表失败: {}", query, e);
            throw new RuntimeException("查询告警列表失败", e);
        }
    }
    
    @Override
    public AlarmDetailResponse queryAlarmDetail(AlarmDetailQuery query) {
        log.info("查询告警详情: {}", query);
        
        try {
            Optional<Alarm> alarmOpt = alarmRepository.findByDetailQuery(query);
            
            if (alarmOpt.isEmpty()) {
                throw new RuntimeException("告警不存在");
            }
            
            return alarmConverter.toDetailResponse(alarmOpt.get());
            
        } catch (Exception e) {
            log.error("查询告警详情失败: {}", query, e);
            throw new RuntimeException("查询告警详情失败", e);
        }
    }
    
    @Override
    public AlarmDetailResponse queryAlarmById(AlarmId alarmId) {
        log.info("根据ID查询告警详情: {}", alarmId);
        
        try {
            Optional<Alarm> alarmOpt = alarmRepository.findById(alarmId);
            
            if (alarmOpt.isEmpty()) {
                throw new RuntimeException("告警不存在: " + alarmId.value());
            }
            
            return alarmConverter.toDetailResponse(alarmOpt.get());
            
        } catch (Exception e) {
            log.error("根据ID查询告警详情失败: {}", alarmId, e);
            throw new RuntimeException("查询告警详情失败", e);
        }
    }
    
    @Override
    public boolean existsById(AlarmId alarmId) {
        log.debug("检查告警是否存在: {}", alarmId);
        
        try {
            return alarmRepository.existsById(alarmId);
        } catch (Exception e) {
            log.error("检查告警是否存在失败: {}", alarmId, e);
            return false;
        }
    }
    
    @Override
    public long countAlarms(AlarmListQuery query) {
        log.info("统计告警总数: {}", query);
        
        try {
            return alarmRepository.countByQuery(query);
        } catch (Exception e) {
            log.error("统计告警总数失败: {}", query, e);
            throw new RuntimeException("统计告警总数失败", e);
        }
    }
}
