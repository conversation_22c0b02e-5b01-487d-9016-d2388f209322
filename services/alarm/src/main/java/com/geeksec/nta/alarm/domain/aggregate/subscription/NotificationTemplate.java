package com.geeksec.nta.alarm.domain.aggregate.subscription;

import com.mybatisflex.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 通知模板实体
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Table(value = "notification_template", comment = "通知模板")
public class NotificationTemplate {
    
    @Id(keyType = KeyType.Generator)
    private String id;
    
    @Column("template_name")
    private String templateName;
    
    @Column("template_type")
    private TemplateType templateType;
    
    @Column("is_default")
    private Boolean isDefault;
    
    /**
     * 主题模板
     */
    @Column("subject_template")
    private String subjectTemplate;
    
    /**
     * 内容模板
     */
    @Column("content_template")
    private String contentTemplate;
    
    /**
     * 模板变量说明 (JSON格式存储)
     */
    @Column("template_variables")
    private Map<String, String> templateVariables;
    
    /**
     * 创建人
     */
    @Column("created_by")
    private String createdBy;
    
    /**
     * 创建时间
     */
    @Column("created_time")
    private LocalDateTime createdTime;
    
    /**
     * 更新人
     */
    @Column("updated_by")
    private String updatedBy;
    
    /**
     * 更新时间
     */
    @Column("updated_time")
    private LocalDateTime updatedTime;
    
    /**
     * 模板类型枚举
     */
    public enum TemplateType {
        /** 邮件模板 */
        EMAIL,
        /** Kafka消息模板 */
        KAFKA
    }
}
