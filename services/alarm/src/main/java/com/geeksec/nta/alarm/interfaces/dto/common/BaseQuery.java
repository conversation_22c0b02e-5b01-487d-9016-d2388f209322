package com.geeksec.nta.alarm.interfaces.dto.common;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 查询参数基类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
public abstract class BaseQuery {
    
    /**
     * 页码（从1开始）
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;
    
    /**
     * 页大小
     */
    @Min(value = 1, message = "页大小必须大于0")
    @Max(value = 1000, message = "页大小不能超过1000")
    private Integer size = 20;
    
    /**
     * 排序字段
     */
    private String sort;
    
    /**
     * 排序方向（asc/desc）
     */
    private String order = "desc";
    
    /**
     * 搜索关键词
     */
    private String search;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 获取偏移量
     */
    public int getOffset() {
        return (page - 1) * size;
    }
    
    /**
     * 获取限制数量
     */
    public int getLimit() {
        return size;
    }
    
    /**
     * 验证时间范围
     */
    public void validateTimeRange() {
        if (startTime != null && endTime != null && startTime.isAfter(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
    }
    
    /**
     * 验证排序参数
     */
    public void validateSort(List<String> allowedFields) {
        if (sort != null && !allowedFields.contains(sort)) {
            throw new IllegalArgumentException("不支持的排序字段: " + sort);
        }
        if (order != null && !List.of("asc", "desc").contains(order.toLowerCase())) {
            throw new IllegalArgumentException("排序方向只能是asc或desc");
        }
    }
    
    /**
     * 是否为升序
     */
    public boolean isAscending() {
        return "asc".equalsIgnoreCase(order);
    }
    
    /**
     * 是否为降序
     */
    public boolean isDescending() {
        return "desc".equalsIgnoreCase(order);
    }
    
    /**
     * 是否有搜索条件
     */
    public boolean hasSearch() {
        return search != null && !search.trim().isEmpty();
    }
    
    /**
     * 是否有时间范围
     */
    public boolean hasTimeRange() {
        return startTime != null || endTime != null;
    }
    
    /**
     * 是否有排序
     */
    public boolean hasSort() {
        return sort != null && !sort.trim().isEmpty();
    }
}
