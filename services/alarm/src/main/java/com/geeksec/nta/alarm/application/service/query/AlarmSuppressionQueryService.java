package com.geeksec.nta.alarm.application.service.query;

import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.application.query.SuppressionListQuery;
import com.geeksec.nta.alarm.domain.valueobject.SuppressionId;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmSuppressionResponse;

import java.util.List;
import java.util.Optional;

/**
 * 告警抑制查询应用服务
 * 负责处理所有抑制规则查询相关的业务逻辑
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmSuppressionQueryService {
    
    /**
     * 分页查询抑制规则
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    PageResultVo<AlarmSuppressionResponse> querySuppressions(SuppressionListQuery query);
    
    /**
     * 获取抑制规则详情
     * 
     * @param suppressionId 抑制规则ID
     * @return 抑制规则详情
     */
    Optional<AlarmSuppressionResponse> getSuppression(SuppressionId suppressionId);
    
    /**
     * 获取所有抑制规则
     * 
     * @return 抑制规则列表
     */
    List<AlarmSuppressionResponse> getAllSuppressions();
    
    /**
     * 获取活跃的抑制规则
     * 
     * @return 活跃抑制规则列表
     */
    List<AlarmSuppressionResponse> getActiveSuppressions();
    
    /**
     * 根据条件查询抑制规则
     * 
     * @param victim 受害者IP（可选）
     * @param attacker 攻击者IP（可选）
     * @param label 告警标签（可选）
     * @return 匹配的抑制规则列表
     */
    List<AlarmSuppressionResponse> getSuppressionsByCondition(String victim, String attacker, String label);
    
    /**
     * 搜索抑制规则
     * 
     * @param keyword 关键词
     * @return 搜索结果
     */
    List<AlarmSuppressionResponse> searchSuppressions(String keyword);
    
    /**
     * 检查抑制规则是否存在
     * 
     * @param suppressionId 抑制规则ID
     * @return 是否存在
     */
    boolean existsSuppression(SuppressionId suppressionId);
    
    /**
     * 检查是否存在重复的抑制规则
     * 
     * @param victim 受害者IP
     * @param attacker 攻击者IP
     * @param label 告警标签
     * @param excludeId 排除的规则ID（用于更新时检查）
     * @return 是否存在重复
     */
    boolean isDuplicateRule(String victim, String attacker, String label, SuppressionId excludeId);
    
    /**
     * 统计抑制规则数量
     * 
     * @return 总数量
     */
    long countSuppressions();
    
    /**
     * 统计活跃抑制规则数量
     * 
     * @return 活跃数量
     */
    long countActiveSuppressions();
    
    /**
     * 获取抑制规则统计信息
     * 
     * @return 统计信息
     */
    SuppressionStatistics getSuppressionStatistics();
    
    /**
     * 抑制规则统计信息
     */
    record SuppressionStatistics(
        long totalRules,
        long activeRules,
        long expiredRules,
        long disabledRules,
        long recentlyUsedRules,
        List<TopSuppressionRule> topUsedRules,
        List<SuppressionTrend> trends
    ) {}
    
    /**
     * 热门抑制规则
     */
    record TopSuppressionRule(
        String ruleId,
        String description,
        long hitCount,
        String lastHitTime
    ) {}
    
    /**
     * 抑制趋势
     */
    record SuppressionTrend(
        String date,
        long totalHits,
        long uniqueAlarms,
        double suppressionRate
    ) {}
}
