package com.geeksec.nta.alarm.domain.service;

import com.geeksec.nta.alarm.domain.aggregate.Alarm;
import com.geeksec.nta.alarm.domain.aggregate.AlarmSuppression;
import com.geeksec.nta.alarm.domain.valueobject.SuppressionRule;

import java.util.List;

/**
 * 抑制规则引擎
 * 负责告警抑制规则的匹配和执行逻辑
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface SuppressionRuleEngine {
    
    /**
     * 检查告警是否应被抑制
     * 
     * @param alarm 告警
     * @param suppressions 抑制规则列表
     * @return 抑制结果
     */
    SuppressionResult shouldSuppress(Alarm alarm, List<AlarmSuppression> suppressions);
    
    /**
     * 检查单个抑制规则是否匹配
     * 
     * @param alarm 告警
     * @param suppression 抑制规则
     * @return 匹配结果
     */
    MatchResult matches(Alarm alarm, AlarmSuppression suppression);
    
    /**
     * 批量检查告警抑制
     * 
     * @param alarms 告警列表
     * @param suppressions 抑制规则列表
     * @return 批量抑制结果
     */
    BatchSuppressionResult batchCheckSuppression(List<Alarm> alarms, List<AlarmSuppression> suppressions);
    
    /**
     * 验证抑制规则的有效性
     * 
     * @param rule 抑制规则
     * @return 验证结果
     */
    ValidationResult validateRule(SuppressionRule rule);
    
    /**
     * 测试抑制规则
     * 
     * @param alarm 测试告警
     * @param rule 抑制规则
     * @return 测试结果
     */
    TestResult testRule(Alarm alarm, SuppressionRule rule);
    
    /**
     * 优化抑制规则集合
     * 移除冗余规则，合并相似规则
     * 
     * @param rules 原始规则列表
     * @return 优化后的规则列表
     */
    OptimizationResult optimizeRules(List<SuppressionRule> rules);
    
    /**
     * 抑制结果
     */
    record SuppressionResult(
        boolean suppressed,
        List<MatchedSuppression> matchedSuppressions,
        String reason,
        long evaluationTime
    ) {}
    
    /**
     * 匹配结果
     */
    record MatchResult(
        boolean matched,
        double confidence,
        String reason,
        List<String> matchedFields
    ) {}
    
    /**
     * 批量抑制结果
     */
    record BatchSuppressionResult(
        int totalAlarms,
        int suppressedAlarms,
        List<AlarmSuppressionPair> results,
        long totalEvaluationTime
    ) {}
    
    /**
     * 验证结果
     */
    record ValidationResult(
        boolean valid,
        List<String> errors,
        List<String> warnings,
        String message
    ) {}
    
    /**
     * 测试结果
     */
    record TestResult(
        boolean matched,
        String explanation,
        List<FieldMatchResult> fieldResults,
        String recommendation
    ) {}
    
    /**
     * 优化结果
     */
    record OptimizationResult(
        List<SuppressionRule> optimizedRules,
        int originalCount,
        int optimizedCount,
        List<String> optimizations,
        String summary
    ) {}
    
    /**
     * 匹配的抑制规则
     */
    record MatchedSuppression(
        AlarmSuppression suppression,
        MatchResult matchResult,
        int priority
    ) {}
    
    /**
     * 告警抑制对
     */
    record AlarmSuppressionPair(
        Alarm alarm,
        SuppressionResult result
    ) {}
    
    /**
     * 字段匹配结果
     */
    record FieldMatchResult(
        String fieldName,
        String expectedValue,
        String actualValue,
        boolean matched,
        String operator
    ) {}
}
