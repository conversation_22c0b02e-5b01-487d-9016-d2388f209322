package com.geeksec.nta.alarm.dto.subscription;

import com.geeksec.nta.alarm.entity.AlarmSubscription.FrequencyType;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警订阅响应 VO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmSubscriptionVo {
    
    /**
     * 订阅ID
     */
    private String id;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 订阅名称
     */
    private String subscriptionName;
    
    /**
     * 订阅描述
     */
    private String description;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 优先级
     */
    private Integer priorityLevel;
    
    /**
     * 匹配规则列表
     */
    private List<SubscriptionRuleDto> matchRules;
    
    /**
     * 通知渠道列表
     */
    private List<NotificationChannelDto> notificationChannels;
    
    /**
     * 通知频率类型
     */
    private FrequencyType frequencyType;
    
    /**
     * 频率控制配置
     */
    private FrequencyConfigDto frequencyConfig;
    
    /**
     * 是否启用免打扰
     */
    private Boolean quietHoursEnabled;
    
    /**
     * 免打扰时间配置
     */
    private QuietHoursConfigDto quietHoursConfig;
    
    /**
     * 触发次数
     */
    private Integer triggerCount;
    
    /**
     * 最后触发时间
     */
    private LocalDateTime lastTriggeredTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
