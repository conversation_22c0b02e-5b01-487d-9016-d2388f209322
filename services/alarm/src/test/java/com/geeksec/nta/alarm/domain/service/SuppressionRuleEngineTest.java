package com.geeksec.nta.alarm.domain.service;

import com.geeksec.nta.alarm.domain.aggregate.Alarm;
import com.geeksec.nta.alarm.domain.aggregate.AlarmSuppression;
import com.geeksec.nta.alarm.domain.valueobject.SuppressionRule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 抑制规则引擎测试
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("抑制规则引擎测试")
class SuppressionRuleEngineTest {
    
    @Mock
    private SuppressionRuleEngine suppressionRuleEngine;
    
    private Alarm testAlarm;
    private AlarmSuppression testSuppression;
    private SuppressionRule testRule;
    
    @BeforeEach
    void setUp() {
        testAlarm = createTestAlarm();
        testSuppression = createTestSuppression();
        testRule = createTestRule();
    }
    
    @Test
    @DisplayName("检查告警抑制 - 应该被抑制")
    void shouldSuppress_ShouldBeSuppressed() {
        // Given
        List<AlarmSuppression> suppressions = List.of(testSuppression);
        SuppressionRuleEngine.SuppressionResult expectedResult = 
                new SuppressionRuleEngine.SuppressionResult(
                        true,
                        List.of(new SuppressionRuleEngine.MatchedSuppression(
                                testSuppression,
                                new SuppressionRuleEngine.MatchResult(true, 1.0, "完全匹配", List.of("victim", "attacker", "label")),
                                1
                        )),
                        "匹配到抑制规则",
                        100L
                );
        
        when(suppressionRuleEngine.shouldSuppress(testAlarm, suppressions))
                .thenReturn(expectedResult);
        
        // When
        SuppressionRuleEngine.SuppressionResult result = 
                suppressionRuleEngine.shouldSuppress(testAlarm, suppressions);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.suppressed()).isTrue();
        assertThat(result.matchedSuppressions()).hasSize(1);
        assertThat(result.reason()).isEqualTo("匹配到抑制规则");
        
        verify(suppressionRuleEngine).shouldSuppress(testAlarm, suppressions);
    }
    
    @Test
    @DisplayName("检查告警抑制 - 不应该被抑制")
    void shouldSuppress_ShouldNotBeSuppressed() {
        // Given
        List<AlarmSuppression> suppressions = List.of(testSuppression);
        SuppressionRuleEngine.SuppressionResult expectedResult = 
                new SuppressionRuleEngine.SuppressionResult(
                        false,
                        List.of(),
                        "未匹配到抑制规则",
                        50L
                );
        
        when(suppressionRuleEngine.shouldSuppress(testAlarm, suppressions))
                .thenReturn(expectedResult);
        
        // When
        SuppressionRuleEngine.SuppressionResult result = 
                suppressionRuleEngine.shouldSuppress(testAlarm, suppressions);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.suppressed()).isFalse();
        assertThat(result.matchedSuppressions()).isEmpty();
        assertThat(result.reason()).isEqualTo("未匹配到抑制规则");
        
        verify(suppressionRuleEngine).shouldSuppress(testAlarm, suppressions);
    }
    
    @Test
    @DisplayName("单个规则匹配 - 匹配成功")
    void matches_MatchSuccess() {
        // Given
        SuppressionRuleEngine.MatchResult expectedResult = 
                new SuppressionRuleEngine.MatchResult(
                        true,
                        0.95,
                        "IP和标签完全匹配",
                        List.of("victim", "attacker", "label")
                );
        
        when(suppressionRuleEngine.matches(testAlarm, testSuppression))
                .thenReturn(expectedResult);
        
        // When
        SuppressionRuleEngine.MatchResult result = 
                suppressionRuleEngine.matches(testAlarm, testSuppression);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.matched()).isTrue();
        assertThat(result.confidence()).isEqualTo(0.95);
        assertThat(result.reason()).isEqualTo("IP和标签完全匹配");
        assertThat(result.matchedFields()).containsExactly("victim", "attacker", "label");
        
        verify(suppressionRuleEngine).matches(testAlarm, testSuppression);
    }
    
    @Test
    @DisplayName("批量检查抑制 - 部分被抑制")
    void batchCheckSuppression_PartialSuppressed() {
        // Given
        List<Alarm> alarms = List.of(testAlarm, createAnotherTestAlarm());
        List<AlarmSuppression> suppressions = List.of(testSuppression);
        
        SuppressionRuleEngine.BatchSuppressionResult expectedResult = 
                new SuppressionRuleEngine.BatchSuppressionResult(
                        2,
                        1,
                        List.of(
                                new SuppressionRuleEngine.AlarmSuppressionPair(
                                        testAlarm,
                                        new SuppressionRuleEngine.SuppressionResult(true, List.of(), "被抑制", 100L)
                                ),
                                new SuppressionRuleEngine.AlarmSuppressionPair(
                                        createAnotherTestAlarm(),
                                        new SuppressionRuleEngine.SuppressionResult(false, List.of(), "未被抑制", 50L)
                                )
                        ),
                        150L
                );
        
        when(suppressionRuleEngine.batchCheckSuppression(alarms, suppressions))
                .thenReturn(expectedResult);
        
        // When
        SuppressionRuleEngine.BatchSuppressionResult result = 
                suppressionRuleEngine.batchCheckSuppression(alarms, suppressions);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.totalAlarms()).isEqualTo(2);
        assertThat(result.suppressedAlarms()).isEqualTo(1);
        assertThat(result.results()).hasSize(2);
        assertThat(result.totalEvaluationTime()).isEqualTo(150L);
        
        verify(suppressionRuleEngine).batchCheckSuppression(alarms, suppressions);
    }
    
    @Test
    @DisplayName("验证抑制规则 - 有效规则")
    void validateRule_ValidRule() {
        // Given
        SuppressionRuleEngine.ValidationResult expectedResult = 
                new SuppressionRuleEngine.ValidationResult(
                        true,
                        List.of(),
                        List.of(),
                        "规则验证通过"
                );
        
        when(suppressionRuleEngine.validateRule(testRule))
                .thenReturn(expectedResult);
        
        // When
        SuppressionRuleEngine.ValidationResult result = 
                suppressionRuleEngine.validateRule(testRule);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.valid()).isTrue();
        assertThat(result.errors()).isEmpty();
        assertThat(result.warnings()).isEmpty();
        assertThat(result.message()).isEqualTo("规则验证通过");
        
        verify(suppressionRuleEngine).validateRule(testRule);
    }
    
    @Test
    @DisplayName("验证抑制规则 - 无效规则")
    void validateRule_InvalidRule() {
        // Given
        SuppressionRule invalidRule = createInvalidRule();
        SuppressionRuleEngine.ValidationResult expectedResult = 
                new SuppressionRuleEngine.ValidationResult(
                        false,
                        List.of("IP地址格式不正确", "标签不能为空"),
                        List.of("建议使用更具体的规则"),
                        "规则验证失败"
                );
        
        when(suppressionRuleEngine.validateRule(invalidRule))
                .thenReturn(expectedResult);
        
        // When
        SuppressionRuleEngine.ValidationResult result = 
                suppressionRuleEngine.validateRule(invalidRule);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.valid()).isFalse();
        assertThat(result.errors()).containsExactly("IP地址格式不正确", "标签不能为空");
        assertThat(result.warnings()).containsExactly("建议使用更具体的规则");
        assertThat(result.message()).isEqualTo("规则验证失败");
        
        verify(suppressionRuleEngine).validateRule(invalidRule);
    }
    
    @Test
    @DisplayName("测试抑制规则 - 匹配")
    void testRule_Match() {
        // Given
        SuppressionRuleEngine.TestResult expectedResult = 
                new SuppressionRuleEngine.TestResult(
                        true,
                        "规则匹配成功，告警将被抑制",
                        List.of(
                                new SuppressionRuleEngine.FieldMatchResult("victim", "*************", "*************", true, "equals"),
                                new SuppressionRuleEngine.FieldMatchResult("attacker", "********", "********", true, "equals"),
                                new SuppressionRuleEngine.FieldMatchResult("label", "SCAN", "SCAN", true, "equals")
                        ),
                        "规则配置正确"
                );
        
        when(suppressionRuleEngine.testRule(testAlarm, testRule))
                .thenReturn(expectedResult);
        
        // When
        SuppressionRuleEngine.TestResult result = 
                suppressionRuleEngine.testRule(testAlarm, testRule);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.matched()).isTrue();
        assertThat(result.explanation()).isEqualTo("规则匹配成功，告警将被抑制");
        assertThat(result.fieldResults()).hasSize(3);
        assertThat(result.recommendation()).isEqualTo("规则配置正确");
        
        verify(suppressionRuleEngine).testRule(testAlarm, testRule);
    }
    
    // 辅助方法
    private Alarm createTestAlarm() {
        return mock(Alarm.class);
    }
    
    private Alarm createAnotherTestAlarm() {
        return mock(Alarm.class);
    }
    
    private AlarmSuppression createTestSuppression() {
        return mock(AlarmSuppression.class);
    }
    
    private SuppressionRule createTestRule() {
        return mock(SuppressionRule.class);
    }
    
    private SuppressionRule createInvalidRule() {
        return mock(SuppressionRule.class);
    }
}
