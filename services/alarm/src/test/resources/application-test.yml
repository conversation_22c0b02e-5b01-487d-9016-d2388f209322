# 测试环境配置
spring:
  profiles:
    active: test
  
  # 数据源配置
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        
  # H2控制台
  h2:
    console:
      enabled: true
      
  # Redis配置（使用嵌入式Redis）
  redis:
    host: localhost
    port: 6370
    timeout: 2000ms
    
  # 缓存配置
  cache:
    type: simple
    
# 日志配置
logging:
  level:
    com.geeksec.nta.alarm: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    
# 告警模块配置
alarm:
  # 分页配置
  pagination:
    default-size: 20
    max-size: 1000
    
  # 缓存配置
  cache:
    ttl: 300 # 5分钟
    max-size: 10000
    
  # 导出配置
  export:
    max-records: 100000
    timeout: 300000 # 5分钟
    
  # 抑制规则配置
  suppression:
    cache-size: 1000
    batch-size: 100
    
  # 订阅配置
  subscription:
    max-rules-per-subscription: 50
    max-channels-per-subscription: 10
    
# 测试数据配置
test:
  data:
    # 是否初始化测试数据
    init-enabled: true
    # 测试数据文件路径
    data-files:
      - classpath:test-data/alarm-types.sql
      - classpath:test-data/alarm-metadata.sql
      
# 外部服务配置（测试环境使用Mock）
external:
  services:
    # Elasticsearch配置
    elasticsearch:
      enabled: false
      mock: true
      
    # 通知服务配置
    notification:
      enabled: false
      mock: true
      
    # 用户服务配置
    user:
      enabled: false
      mock: true
