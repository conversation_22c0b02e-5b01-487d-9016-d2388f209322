# Alarm模块与Flink-Jobs集成API文档

## 概述

alarm模块负责告警抑制规则的管理，flink-jobs/alarm-processor负责实际的告警处理和抑制规则执行。

## 职责分工

### alarm模块职责
- 抑制规则的CRUD管理
- 抑制规则配置和维护
- 为flink-jobs提供规则查询接口
- 规则变更通知

### flink-jobs/alarm-processor职责
- 实时告警流处理
- 抑制规则的实际执行
- 调用alarm模块获取最新规则
- 告警抑制决策

## API接口设计

### 1. 获取活跃抑制规则

**接口**: `GET /api/v1/suppressions/rules/active`

**描述**: 获取当前生效的所有抑制规则

**响应格式**:
```json
{
  "success": true,
  "data": [
    {
      "ruleId": "rule-001",
      "victim": "*************",
      "attacker": "********", 
      "label": "SCAN",
      "description": "扫描抑制规则",
      "enabled": true,
      "createTime": 1640995200000,
      "expireTime": 1672531200000,
      "operator": "admin"
    }
  ],
  "message": "查询成功"
}
```

### 2. 根据条件查询抑制规则

**接口**: `GET /api/v1/suppressions/rules/search`

**参数**:
- `victim`: 受害者IP（可选）
- `attacker`: 攻击者IP（可选）
- `label`: 告警标签（可选）

**描述**: 根据条件查询匹配的抑制规则

**使用场景**: flink-jobs可以根据具体的告警信息查询相关规则

### 3. 检查规则是否存在

**接口**: `GET /api/v1/suppressions/rules/exists`

**参数**:
- `victim`: 受害者IP（必填）
- `attacker`: 攻击者IP（必填）
- `label`: 告警标签（必填）

**描述**: 快速检查是否存在匹配的抑制规则

**响应格式**:
```json
{
  "success": true,
  "data": true,
  "message": "检查完成"
}
```

### 4. 获取规则最后更新时间

**接口**: `GET /api/v1/suppressions/rules/last-update`

**描述**: 获取抑制规则的最后更新时间戳

**使用场景**: flink-jobs可以定期检查此时间戳，判断是否需要重新加载规则

**响应格式**:
```json
{
  "success": true,
  "data": 1640995200000,
  "message": "查询成功"
}
```

## 集成方案

### 1. 规则加载策略

flink-jobs应该采用以下策略加载和更新抑制规则：

1. **启动时加载**: 作业启动时调用`/rules/active`接口加载所有活跃规则
2. **定期检查**: 每隔一定时间（如30秒）调用`/rules/last-update`检查是否有更新
3. **增量更新**: 如果发现有更新，重新调用`/rules/active`接口更新规则
4. **缓存机制**: 在flink-jobs中缓存规则，避免每次处理告警都调用接口

### 2. 规则匹配逻辑

flink-jobs中的抑制规则匹配逻辑应该包括：

```java
// 伪代码示例
public boolean shouldSuppressAlarm(Alarm alarm, List<SuppressionRule> rules) {
    for (SuppressionRule rule : rules) {
        if (matchesRule(alarm, rule)) {
            return true;
        }
    }
    return false;
}

private boolean matchesRule(Alarm alarm, SuppressionRule rule) {
    // 检查受害者IP
    if (rule.getVictim() != null && !rule.getVictim().equals(alarm.getVictimIp())) {
        return false;
    }
    
    // 检查攻击者IP
    if (rule.getAttacker() != null && !rule.getAttacker().equals(alarm.getAttackerIp())) {
        return false;
    }
    
    // 检查告警标签
    if (rule.getLabel() != null && !rule.getLabel().equals(alarm.getAlarmType())) {
        return false;
    }
    
    // 检查规则是否过期
    if (rule.getExpireTime() > 0 && System.currentTimeMillis() > rule.getExpireTime()) {
        return false;
    }
    
    return true;
}
```

### 3. 性能优化建议

1. **规则缓存**: 在flink-jobs中使用本地缓存存储规则，减少网络调用
2. **批量查询**: 如果需要查询多个规则，尽量使用批量接口
3. **异步更新**: 规则更新可以异步进行，不影响告警处理的实时性
4. **规则索引**: 在flink-jobs中对规则建立索引，提高匹配效率

### 4. 错误处理

flink-jobs应该处理以下异常情况：

1. **网络异常**: 无法连接alarm模块时，使用本地缓存的规则
2. **服务异常**: alarm模块返回错误时，记录日志并继续处理
3. **规则格式错误**: 对于格式不正确的规则，记录警告并跳过
4. **超时处理**: 设置合理的超时时间，避免影响告警处理性能

## 监控和日志

### 1. 关键指标

- 规则加载次数和耗时
- 规则匹配次数和命中率
- 接口调用成功率和响应时间
- 抑制的告警数量和比例

### 2. 日志记录

- 规则加载和更新日志
- 规则匹配和抑制决策日志
- 接口调用异常日志
- 性能统计日志

## 示例配置

### flink-jobs配置示例

```yaml
alarm:
  suppression:
    # alarm模块服务地址
    service-url: "http://alarm-service:8080"
    # 规则更新检查间隔（秒）
    update-check-interval: 30
    # 规则缓存大小
    cache-size: 10000
    # 接口调用超时时间（毫秒）
    timeout: 5000
    # 重试次数
    retry-count: 3
```

### 使用示例

```java
// flink-jobs中的使用示例
@Component
public class SuppressionRuleManager {
    
    private final RestTemplate restTemplate;
    private final Map<String, SuppressionRule> ruleCache = new ConcurrentHashMap<>();
    private volatile long lastUpdateTime = 0;
    
    @Scheduled(fixedDelay = 30000) // 30秒检查一次
    public void checkForUpdates() {
        try {
            long serverUpdateTime = getLastUpdateTime();
            if (serverUpdateTime > lastUpdateTime) {
                loadActiveRules();
                lastUpdateTime = serverUpdateTime;
            }
        } catch (Exception e) {
            log.warn("检查规则更新失败", e);
        }
    }
    
    public boolean shouldSuppress(String victim, String attacker, String label) {
        return ruleCache.values().stream()
                .anyMatch(rule -> matchesRule(victim, attacker, label, rule));
    }
}
```
