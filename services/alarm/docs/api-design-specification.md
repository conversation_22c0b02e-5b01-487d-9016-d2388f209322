# Alarm模块RESTful API设计规范

## 模块职责说明

**重要**: alarm模块只负责告警抑制规则的管理，不负责规则执行。实际的抑制规则执行由`flink-jobs/alarm-processor`负责。

### alarm模块职责
- 抑制规则的CRUD管理
- 抑制规则配置和维护  
- 为flink-jobs提供规则查询接口
- 规则变更通知

### flink-jobs/alarm-processor职责
- 实时告警流处理
- 抑制规则的实际执行
- 调用alarm模块获取最新规则
- 告警抑制决策

## API设计原则

### 1.1 资源命名规范
- 使用名词复数形式表示资源集合：`/suppressions`
- 使用层级结构表示资源关系：`/suppressions/rules/active`
- 使用kebab-case命名：`/last-update`

### 1.2 HTTP方法使用
- `GET`: 查询资源，幂等操作
- `POST`: 创建资源，非幂等操作
- `PUT`: 完整更新资源，幂等操作
- `PATCH`: 部分更新资源，幂等操作
- `DELETE`: 删除资源，幂等操作

### 1.3 状态码规范
- `200 OK`: 成功返回数据
- `201 Created`: 成功创建资源
- `204 No Content`: 成功执行但无返回内容
- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 未认证
- `403 Forbidden`: 无权限
- `404 Not Found`: 资源不存在
- `409 Conflict`: 资源冲突
- `422 Unprocessable Entity`: 业务逻辑错误
- `500 Internal Server Error`: 服务器内部错误

## 告警抑制API设计

### 抑制规则CRUD操作
```
GET    /api/v1/suppressions              # 查询抑制规则列表
GET    /api/v1/suppressions/{id}         # 查询抑制规则详情
POST   /api/v1/suppressions              # 创建抑制规则
PUT    /api/v1/suppressions/{id}         # 完整更新抑制规则
PATCH  /api/v1/suppressions/{id}         # 部分更新抑制规则
DELETE /api/v1/suppressions/{id}         # 删除抑制规则
DELETE /api/v1/suppressions              # 批量删除抑制规则
```

### 抑制规则提供接口（供flink-jobs调用）
```
GET    /api/v1/suppressions/rules/active        # 获取活跃抑制规则
GET    /api/v1/suppressions/rules/search        # 根据条件查询抑制规则
GET    /api/v1/suppressions/rules/exists        # 检查规则是否存在
GET    /api/v1/suppressions/rules/last-update   # 获取规则最后更新时间
```

### 抑制统计
```
GET    /api/v1/suppressions/statistics   # 获取抑制统计信息
```

## 查询参数规范

### 分页参数
```
page: 页码，从1开始
size: 页大小，默认20，最大1000
```

### 排序参数
```
sort: 排序字段，支持多字段排序
order: 排序方向，asc/desc
```

### 过滤参数
```
filter: 过滤条件，支持多种操作符
search: 全文搜索关键词
```

### 时间范围参数
```
start_time: 开始时间，ISO 8601格式
end_time: 结束时间，ISO 8601格式
```

## 响应格式规范

### 成功响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 分页响应格式
```json
{
  "success": true,
  "data": {
    "records": [],
    "total": 100,
    "page": 1,
    "size": 20,
    "pages": 5
  },
  "message": "查询成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "参数验证失败",
    "details": [
      {
        "field": "email",
        "message": "邮箱格式不正确"
      }
    ]
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```
