# Alarm模块领域模型设计

## 模块职责说明

**重要**: alarm模块只负责告警抑制规则的管理，不负责规则执行。实际的抑制规则执行由`flink-jobs/alarm-processor`负责。

## 聚合根设计

### AlarmSuppression聚合根

```java
public class AlarmSuppression {
    private SuppressionId id;
    private SuppressionRule rule;
    private SuppressionStatus status;
    private Timestamp createTime;
    private Timestamp updateTime;
    private Timestamp expireTime;
    private String operator;
    private String description;
    
    // 业务方法
    public boolean isActive();
    public boolean isExpired(Timestamp currentTime);
    public void enable(String operator);
    public void disable(String operator);
    public void extend(Duration duration, String operator);
    public void revoke(String reason, String operator);
    public SuppressionRuleDto toDto(); // 转换为DTO供flink-jobs使用
}
```

## 值对象设计

### SuppressionId
```java
public record SuppressionId(String value) {
    public SuppressionId {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("SuppressionId不能为空");
        }
    }
}
```

### SuppressionRule
```java
public record SuppressionRule(
    String victim,      // 受害者IP，支持通配符
    String attacker,    // 攻击者IP，支持通配符  
    String label,       // 告警标签
    String description  // 规则描述
) {
    public SuppressionRule {
        // 验证规则有效性
        validateRule(victim, attacker, label);
    }
    
    private static void validateRule(String victim, String attacker, String label) {
        if (victim == null && attacker == null && label == null) {
            throw new IllegalArgumentException("抑制规则至少需要指定一个条件");
        }
        // 其他验证逻辑...
    }
    
    public boolean matches(String victimIp, String attackerIp, String alarmLabel) {
        // 匹配逻辑（仅用于规则验证，实际匹配在flink-jobs中执行）
        return matchesPattern(victim, victimIp) &&
               matchesPattern(attacker, attackerIp) &&
               matchesPattern(label, alarmLabel);
    }
}
```

### SuppressionStatus
```java
public enum SuppressionStatus {
    ACTIVE("生效中"),
    DISABLED("已禁用"),
    EXPIRED("已过期"),
    REVOKED("已撤销");
    
    private final String description;
    
    SuppressionStatus(String description) {
        this.description = description;
    }
    
    public boolean isEffective() {
        return this == ACTIVE;
    }
}
```

## 领域服务设计

### SuppressionRuleValidator
```java
public interface SuppressionRuleValidator {
    /**
     * 验证抑制规则的有效性
     */
    ValidationResult validate(SuppressionRule rule);
    
    /**
     * 检查规则是否重复
     */
    boolean isDuplicate(SuppressionRule rule, SuppressionId excludeId);
    
    /**
     * 测试规则匹配效果（仅用于规则验证）
     */
    TestResult testRule(SuppressionRule rule, String victim, String attacker, String label);
}
```

### SuppressionRuleProvider
```java
public interface SuppressionRuleProvider {
    /**
     * 获取所有活跃规则（供flink-jobs调用）
     */
    List<SuppressionRuleDto> getActiveRules();
    
    /**
     * 根据条件查询规则
     */
    List<SuppressionRuleDto> getRulesByCondition(String victim, String attacker, String label);
    
    /**
     * 获取规则最后更新时间
     */
    long getLastUpdateTime();
}
```

## 仓储接口设计

### AlarmSuppressionRepository
```java
public interface AlarmSuppressionRepository {
    void save(AlarmSuppression suppression);
    Optional<AlarmSuppression> findById(SuppressionId id);
    List<AlarmSuppression> findByCondition(SuppressionQueryCondition condition);
    List<AlarmSuppression> findActiveSuppressions();
    PageResult<AlarmSuppression> findPageByCondition(
        SuppressionQueryCondition condition, 
        Pageable pageable
    );
    void delete(SuppressionId id);
    boolean existsByRule(SuppressionRule rule, SuppressionId excludeId);
    long getLastUpdateTime();
    SuppressionStatistics getStatistics();
}
```

## 领域事件设计

### SuppressionRuleCreatedEvent
```java
public record SuppressionRuleCreatedEvent(
    SuppressionId suppressionId,
    SuppressionRule rule,
    String operator,
    Timestamp occurredAt
) implements DomainEvent {
}
```

### SuppressionRuleUpdatedEvent
```java
public record SuppressionRuleUpdatedEvent(
    SuppressionId suppressionId,
    SuppressionRule oldRule,
    SuppressionRule newRule,
    String operator,
    Timestamp occurredAt
) implements DomainEvent {
}
```

### SuppressionRuleDeletedEvent
```java
public record SuppressionRuleDeletedEvent(
    SuppressionId suppressionId,
    SuppressionRule rule,
    String operator,
    String reason,
    Timestamp occurredAt
) implements DomainEvent {
}
```

## 数据传输对象

### SuppressionRuleDto
```java
public record SuppressionRuleDto(
    String ruleId,
    String victim,
    String attacker,
    String label,
    String description,
    boolean enabled,
    long createTime,
    long expireTime,
    String operator
) {
    /**
     * 供flink-jobs使用的规则匹配方法
     */
    public boolean matches(String victimIp, String attackerIp, String alarmLabel) {
        // 实际匹配逻辑应该在flink-jobs中实现
        // 这里只是示例
        return matchesPattern(victim, victimIp) &&
               matchesPattern(attacker, attackerIp) &&
               matchesPattern(label, alarmLabel);
    }
    
    private boolean matchesPattern(String pattern, String value) {
        if (pattern == null || pattern.isEmpty()) {
            return true; // 空模式匹配所有
        }
        if (pattern.equals("*")) {
            return true; // 通配符匹配所有
        }
        return pattern.equals(value); // 精确匹配
    }
}
```

## 与flink-jobs的集成

### 规则同步机制

1. **推送模式**: 当规则发生变更时，通过领域事件通知flink-jobs
2. **拉取模式**: flink-jobs定期调用API获取最新规则
3. **混合模式**: 结合推送和拉取，确保数据一致性

### 性能考虑

1. **缓存策略**: alarm模块提供缓存的规则数据
2. **增量更新**: 支持增量获取变更的规则
3. **批量接口**: 提供批量查询接口减少网络开销

### 数据一致性

1. **版本控制**: 为规则数据添加版本号
2. **校验机制**: 提供数据校验接口
3. **回滚支持**: 支持规则变更的回滚操作
