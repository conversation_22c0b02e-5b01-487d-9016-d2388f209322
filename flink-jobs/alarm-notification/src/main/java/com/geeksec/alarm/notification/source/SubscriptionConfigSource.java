package com.geeksec.alarm.notification.source;

import java.util.List;

import org.apache.flink.streaming.api.functions.source.SourceFunction;

import com.geeksec.alarm.notification.client.AlarmServiceClient;
import com.geeksec.alarm.notification.model.NotificationSubscription;

import lombok.extern.slf4j.Slf4j;

/**
 * 订阅配置数据源
 * 负责从告警服务获取订阅配置数据
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class SubscriptionConfigSource implements SourceFunction<NotificationSubscription> {
    
    private static final long serialVersionUID = 1L;
    
    private final AlarmServiceClient alarmServiceClient;
    
    private volatile boolean running = true;
    
    public SubscriptionConfigSource(AlarmServiceClient alarmServiceClient) {
        this.alarmServiceClient = alarmServiceClient;
    }
    
    @Override
    public void run(SourceFunction.SourceContext<NotificationSubscription> ctx) throws Exception {
        log.info("开始初始化订阅配置数据源");
        
        try {
            // 初始化时拉取一次订阅配置
            List<NotificationSubscription> subscriptions = alarmServiceClient.getAllActiveSubscriptions();
            log.info("成功获取到 {} 个活动订阅配置", subscriptions.size());
            
            for (NotificationSubscription subscription : subscriptions) {
                if (!running) {
                    break;
                }
                ctx.collect(subscription);
                log.debug("发送订阅配置: {}", subscription.getSubscriptionId());
            }
            
            log.info("订阅配置初始化完成，共发送 {} 个订阅配置", subscriptions.size());
            
        } catch (Exception e) {
            log.error("获取订阅配置失败", e);
            throw e;
        }
    }
    
    @Override
    public void cancel() {
        log.info("取消订阅配置数据源");
        running = false;
    }
    
    /**
     * 创建默认的订阅配置数据源
     * 
     * @param alarmServiceClient 告警服务客户端
     * @return 订阅配置数据源
     */
    public static SubscriptionConfigSource createDefault(AlarmServiceClient alarmServiceClient) {
        return new SubscriptionConfigSource(alarmServiceClient);
    }
}
